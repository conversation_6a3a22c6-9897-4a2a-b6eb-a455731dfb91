import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class GroupsRecord extends FirestoreRecord {
  GroupsRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "group_id" field.
  int? _groupId;
  int get groupId => _groupId ?? 0;
  bool hasGroupId() => _groupId != null;

  // "group_name" field.
  String? _groupName;
  String get groupName => _groupName ?? '';
  bool hasGroupName() => _groupName != null;

  // "group_members" field.
  List<DocumentReference>? _groupMembers;
  List<DocumentReference> get groupMembers => _groupMembers ?? const [];
  bool hasGroupMembers() => _groupMembers != null;

  // "group_admins" field.
  List<DocumentReference>? _groupAdmins;
  List<DocumentReference> get groupAdmins => _groupAdmins ?? const [];
  bool hasGroupAdmins() => _groupAdmins != null;

  // "group_pages" field.
  List<DocumentReference>? _groupPages;
  List<DocumentReference> get groupPages => _groupPages ?? const [];
  bool hasGroupPages() => _groupPages != null;

  // "group_description" field.
  String? _groupDescription;
  String get groupDescription => _groupDescription ?? '';
  bool hasGroupDescription() => _groupDescription != null;

  // "group_image" field.
  String? _groupImage;
  String get groupImage => _groupImage ?? '';
  bool hasGroupImage() => _groupImage != null;

  // "group_activity" field.
  List<DocumentReference>? _groupActivity;
  List<DocumentReference> get groupActivity => _groupActivity ?? const [];
  bool hasGroupActivity() => _groupActivity != null;

  // "group_posts" field.
  List<DocumentReference>? _groupPosts;
  List<DocumentReference> get groupPosts => _groupPosts ?? const [];
  bool hasGroupPosts() => _groupPosts != null;

  // "hastags" field.
  List<String>? _hastags;
  List<String> get hastags => _hastags ?? const [];
  bool hasHastags() => _hastags != null;

  // "rules" field.
  String? _rules;
  String get rules => _rules ?? '';
  bool hasRules() => _rules != null;

  // "typesRef" field.
  List<String>? _typesRef;
  List<String> get typesRef => _typesRef ?? const [];
  bool hasTypesRef() => _typesRef != null;

  // "emailBool" field.
  bool? _emailBool;
  bool get emailBool => _emailBool ?? false;
  bool hasEmailBool() => _emailBool != null;

  // "phoneBool" field.
  bool? _phoneBool;
  bool get phoneBool => _phoneBool ?? false;
  bool hasPhoneBool() => _phoneBool != null;

  // "nameBool" field.
  bool? _nameBool;
  bool get nameBool => _nameBool ?? false;
  bool hasNameBool() => _nameBool != null;

  // "email_participant" field.
  List<String>? _emailParticipant;
  List<String> get emailParticipant => _emailParticipant ?? const [];
  bool hasEmailParticipant() => _emailParticipant != null;

  // "number_participant" field.
  List<String>? _numberParticipant;
  List<String> get numberParticipant => _numberParticipant ?? const [];
  bool hasNumberParticipant() => _numberParticipant != null;

  // "name_participant" field.
  List<String>? _nameParticipant;
  List<String> get nameParticipant => _nameParticipant ?? const [];
  bool hasNameParticipant() => _nameParticipant != null;

  // "ruleBool" field.
  bool? _ruleBool;
  bool get ruleBool => _ruleBool ?? false;
  bool hasRuleBool() => _ruleBool != null;

  // "group_moderator" field.
  List<DocumentReference>? _groupModerator;
  List<DocumentReference> get groupModerator => _groupModerator ?? const [];
  bool hasGroupModerator() => _groupModerator != null;

  // "group_pending_member" field.
  List<DocumentReference>? _groupPendingMember;
  List<DocumentReference> get groupPendingMember =>
      _groupPendingMember ?? const [];
  bool hasGroupPendingMember() => _groupPendingMember != null;

  // "requestBool" field.
  bool? _requestBool;
  bool get requestBool => _requestBool ?? false;
  bool hasRequestBool() => _requestBool != null;

  void _initializeFields() {
    _groupId = castToType<int>(snapshotData['group_id']);
    _groupName = snapshotData['group_name'] as String?;
    _groupMembers = getDataList(snapshotData['group_members']);
    _groupAdmins = getDataList(snapshotData['group_admins']);
    _groupPages = getDataList(snapshotData['group_pages']);
    _groupDescription = snapshotData['group_description'] as String?;
    _groupImage = snapshotData['group_image'] as String?;
    _groupActivity = getDataList(snapshotData['group_activity']);
    _groupPosts = getDataList(snapshotData['group_posts']);
    _hastags = getDataList(snapshotData['hastags']);
    _rules = snapshotData['rules'] as String?;
    _typesRef = getDataList(snapshotData['typesRef']);
    _emailBool = snapshotData['emailBool'] as bool?;
    _phoneBool = snapshotData['phoneBool'] as bool?;
    _nameBool = snapshotData['nameBool'] as bool?;
    _emailParticipant = getDataList(snapshotData['email_participant']);
    _numberParticipant = getDataList(snapshotData['number_participant']);
    _nameParticipant = getDataList(snapshotData['name_participant']);
    _ruleBool = snapshotData['ruleBool'] as bool?;
    _groupModerator = getDataList(snapshotData['group_moderator']);
    _groupPendingMember = getDataList(snapshotData['group_pending_member']);
    _requestBool = snapshotData['requestBool'] as bool?;
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('groups');

  static Stream<GroupsRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => GroupsRecord.fromSnapshot(s));

  static Future<GroupsRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => GroupsRecord.fromSnapshot(s));

  static GroupsRecord fromSnapshot(DocumentSnapshot snapshot) => GroupsRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static GroupsRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      GroupsRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'GroupsRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is GroupsRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createGroupsRecordData({
  int? groupId,
  String? groupName,
  String? groupDescription,
  String? groupImage,
  String? rules,
  bool? emailBool,
  bool? phoneBool,
  bool? nameBool,
  bool? ruleBool,
  bool? requestBool,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'group_id': groupId,
      'group_name': groupName,
      'group_description': groupDescription,
      'group_image': groupImage,
      'rules': rules,
      'emailBool': emailBool,
      'phoneBool': phoneBool,
      'nameBool': nameBool,
      'ruleBool': ruleBool,
      'requestBool': requestBool,
    }.withoutNulls,
  );

  return firestoreData;
}

class GroupsRecordDocumentEquality implements Equality<GroupsRecord> {
  const GroupsRecordDocumentEquality();

  @override
  bool equals(GroupsRecord? e1, GroupsRecord? e2) {
    const listEquality = ListEquality();
    return e1?.groupId == e2?.groupId &&
        e1?.groupName == e2?.groupName &&
        listEquality.equals(e1?.groupMembers, e2?.groupMembers) &&
        listEquality.equals(e1?.groupAdmins, e2?.groupAdmins) &&
        listEquality.equals(e1?.groupPages, e2?.groupPages) &&
        e1?.groupDescription == e2?.groupDescription &&
        e1?.groupImage == e2?.groupImage &&
        listEquality.equals(e1?.groupActivity, e2?.groupActivity) &&
        listEquality.equals(e1?.groupPosts, e2?.groupPosts) &&
        listEquality.equals(e1?.hastags, e2?.hastags) &&
        e1?.rules == e2?.rules &&
        listEquality.equals(e1?.typesRef, e2?.typesRef) &&
        e1?.emailBool == e2?.emailBool &&
        e1?.phoneBool == e2?.phoneBool &&
        e1?.nameBool == e2?.nameBool &&
        listEquality.equals(e1?.emailParticipant, e2?.emailParticipant) &&
        listEquality.equals(e1?.numberParticipant, e2?.numberParticipant) &&
        listEquality.equals(e1?.nameParticipant, e2?.nameParticipant) &&
        e1?.ruleBool == e2?.ruleBool &&
        listEquality.equals(e1?.groupModerator, e2?.groupModerator) &&
        listEquality.equals(e1?.groupPendingMember, e2?.groupPendingMember) &&
        e1?.requestBool == e2?.requestBool;
  }

  @override
  int hash(GroupsRecord? e) => const ListEquality().hash([
        e?.groupId,
        e?.groupName,
        e?.groupMembers,
        e?.groupAdmins,
        e?.groupPages,
        e?.groupDescription,
        e?.groupImage,
        e?.groupActivity,
        e?.groupPosts,
        e?.hastags,
        e?.rules,
        e?.typesRef,
        e?.emailBool,
        e?.phoneBool,
        e?.nameBool,
        e?.emailParticipant,
        e?.numberParticipant,
        e?.nameParticipant,
        e?.ruleBool,
        e?.groupModerator,
        e?.groupPendingMember,
        e?.requestBool
      ]);

  @override
  bool isValidKey(Object? o) => o is GroupsRecord;
}
