import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/cloud_functions/cloud_functions.dart';
import '/backend/push_notifications/push_notifications_util.dart';
import '/flutter_flow/flutter_flow_mux_broadcast.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/index.dart';
import 'dart:async';
import 'dart:io' show Platform;
import 'package:apivideo_live_stream/apivideo_live_stream.dart';
import 'package:flutter/services.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'livestreamer_model.dart';
export 'livestreamer_model.dart';

class LivestreamerWidget extends StatefulWidget {
  const LivestreamerWidget({
    super.key,
    this.streamName,
    this.streamRef,
  });

  final String? streamName;
  final DocumentReference? streamRef;

  static String routeName = 'livestreamer';
  static String routePath = '/livestreamer';

  @override
  State<LivestreamerWidget> createState() => _LivestreamerWidgetState();
}

class _LivestreamerWidgetState extends State<LivestreamerWidget> {
  late LivestreamerModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  String? muxBroadcastPlaybackUrl;
  bool muxBroadcastIsLive = false;
  LiveStreamController? muxBroadcastController;
  final _initialAudioConfig = AudioConfig(
    channel: Channel.stereo,
  );
  final _initialVideoConfig = VideoConfig.withDefaultBitrate(
    resolution: Resolution.RESOLUTION_720,
  );
  // variables for managing camera states
  bool _isCameraInitialized = false;
  bool _isFrontCamSelected = false;
  final _stopwatch = Stopwatch();
  String? _durationString;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LivestreamerModel());

    if (Platform.isAndroid || Platform.isIOS) {
      _initCamera();
    }

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.dispose();

    _stopwatch.stop();
    _timer?.cancel();
    WakelockPlus.disable();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  FFLocalizations.of(context).getText(
                    'sk0qn7zb' /* WiseBook ؤ */,
                  ),
                  style: FlutterFlowTheme.of(context).headlineLarge.override(
                        font: GoogleFonts.outfit(
                          fontWeight: FlutterFlowTheme.of(context)
                              .headlineLarge
                              .fontWeight,
                          fontStyle: FlutterFlowTheme.of(context)
                              .headlineLarge
                              .fontStyle,
                        ),
                        letterSpacing: 0.0,
                        fontWeight: FlutterFlowTheme.of(context)
                            .headlineLarge
                            .fontWeight,
                        fontStyle: FlutterFlowTheme.of(context)
                            .headlineLarge
                            .fontStyle,
                      ),
                ),
                Container(
                  height: 650.0,
                  decoration: BoxDecoration(),
                  child: Align(
                    alignment: AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(
                          10.0, 40.0, 10.0, 10.0),
                      child: FlutterFlowMuxBroadcast(
                        isCameraInitialized: _isCameraInitialized,
                        isStreaming: muxBroadcastIsLive,
                        durationString: _durationString,
                        borderRadius: BorderRadius.circular(0.0),
                        controller: muxBroadcastController,
                        videoConfig: _initialVideoConfig,
                        onCameraRotateButtonTap: () async {
                          await switchCamera();
                          safeSetState(
                              () => _isFrontCamSelected = !_isFrontCamSelected);
                        },
                        startButtonText: FFLocalizations.of(context).getText(
                          '92j8mkvf' /* Start Stream */,
                        ),
                        startButtonIcon: Icon(
                          Icons.play_arrow_rounded,
                          color: Colors.white,
                          size: 24.0,
                        ),
                        startButtonOptions: FFButtonOptions(
                          width: 160.0,
                          height: 50.0,
                          color: FlutterFlowTheme.of(context).error,
                          textStyle:
                              FlutterFlowTheme.of(context).titleSmall.override(
                                    font: GoogleFonts.readexPro(
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontStyle,
                                    ),
                                    color: Colors.white,
                                    letterSpacing: 0.0,
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontStyle,
                                  ),
                          elevation: 0.0,
                          borderSide: BorderSide(
                            color: Colors.transparent,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(40.0),
                        ),
                        liveIcon: FaIcon(
                          FontAwesomeIcons.solidCircle,
                          color: Colors.red,
                          size: 10.0,
                        ),
                        liveText: FFLocalizations.of(context).getText(
                          '51zzw1dx' /* Live */,
                        ),
                        liveTextStyle:
                            FlutterFlowTheme.of(context).titleSmall.override(
                                  font: GoogleFonts.readexPro(
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontStyle,
                                  ),
                                  color: Colors.red,
                                  letterSpacing: 0.0,
                                  fontWeight: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .fontWeight,
                                  fontStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .fontStyle,
                                ),
                        liveTextBackgroundColor:
                            FlutterFlowTheme.of(context).primaryBackground,
                        durationTextStyle:
                            FlutterFlowTheme.of(context).titleSmall.override(
                                  font: GoogleFonts.readexPro(
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontStyle,
                                  ),
                                  color: Colors.red,
                                  letterSpacing: 0.0,
                                  fontWeight: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .fontWeight,
                                  fontStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .fontStyle,
                                ),
                        durationTextBackgroundColor: Color(0x8A000000),
                        liveContainerBorderRadius: BorderRadius.circular(8.0),
                        durationContainerBorderRadius:
                            BorderRadius.circular(8.0),
                        rotateButtonColor: FlutterFlowTheme.of(context).accent2,
                        rotateButtonIcon: Icon(
                          Icons.flip_camera_android,
                          color: Colors.white,
                          size: 24.0,
                        ),
                        stopButtonIcon: Icon(
                          Icons.stop_rounded,
                          color: Colors.white,
                          size: 30.0,
                        ),
                        stopButtonColor: Colors.red,
                        onStartButtonTap: () async {
                          await startStreaming();

                          var streamsRecordReference =
                              StreamsRecord.collection.doc();
                          await streamsRecordReference
                              .set(createStreamsRecordData(
                            name: widget.streamName,
                            isLive: true,
                            url: muxBroadcastPlaybackUrl,
                            time: getCurrentTimestamp,
                            uid: currentUserReference?.id,
                          ));
                          _model.createdDoc = StreamsRecord.getDocumentFromData(
                              createStreamsRecordData(
                                name: widget.streamName,
                                isLive: true,
                                url: muxBroadcastPlaybackUrl,
                                time: getCurrentTimestamp,
                                uid: currentUserReference?.id,
                              ),
                              streamsRecordReference);

                          safeSetState(() {});
                        },
                        onStopButtonTap: () async {
                          stopStreaming();

                          await _model.createdDoc!.reference
                              .update(createStreamsRecordData(
                            isLive: false,
                          ));

                          context.pushNamed(StreamsWidget.routeName);
                        },
                      ),
                    ),
                  ),
                ),
                if (_model.createdDoc?.isLive ?? true)
                  FFButtonWidget(
                    onPressed: () async {
                      var activityRecordReference =
                          ActivityRecord.collection.doc();
                      await activityRecordReference.set({
                        ...createActivityRecordData(
                          timePosted: getCurrentTimestamp,
                          owner: currentUserReference,
                          type: 'Stream Invite',
                          description: 'You\'re invited in a stream',
                          streamBool: true,
                          streamRef: _model.createdDoc?.reference,
                        ),
                        ...mapToFirestore(
                          {
                            'notifyUsers':
                                (currentUserDocument?.follow.toList() ?? []),
                          },
                        ),
                      });
                      _model.inviteUsers = ActivityRecord.getDocumentFromData({
                        ...createActivityRecordData(
                          timePosted: getCurrentTimestamp,
                          owner: currentUserReference,
                          type: 'Stream Invite',
                          description: 'You\'re invited in a stream',
                          streamBool: true,
                          streamRef: _model.createdDoc?.reference,
                        ),
                        ...mapToFirestore(
                          {
                            'notifyUsers':
                                (currentUserDocument?.follow.toList() ?? []),
                          },
                        ),
                      }, activityRecordReference);
                      triggerPushNotification(
                        notificationTitle: 'Stream Invite',
                        notificationText: 'You are invited ',
                        userRefs: (currentUserDocument?.follow.toList() ?? [])
                            .toList(),
                        initialPageName: 'livestreamer',
                        parameterData: {
                          'streamName': widget.streamName,
                        },
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Invited all your followers',
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                          duration: Duration(milliseconds: 4000),
                          backgroundColor: FlutterFlowTheme.of(context).primary,
                        ),
                      );

                      safeSetState(() {});
                    },
                    text: FFLocalizations.of(context).getText(
                      'ab09hv1l' /* Invite Others */,
                    ),
                    options: FFButtonOptions(
                      height: 40.0,
                      padding:
                          EdgeInsetsDirectional.fromSTEB(24.0, 0.0, 24.0, 0.0),
                      iconPadding:
                          EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                      color: FlutterFlowTheme.of(context).primary,
                      textStyle:
                          FlutterFlowTheme.of(context).titleSmall.override(
                                font: GoogleFonts.readexPro(
                                  fontWeight: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .fontWeight,
                                  fontStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .fontStyle,
                                ),
                                color: Colors.white,
                                letterSpacing: 0.0,
                                fontWeight: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .fontWeight,
                                fontStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .fontStyle,
                              ),
                      elevation: 3.0,
                      borderSide: BorderSide(
                        color: Colors.transparent,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(14.0),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _initCamera() async {
    muxBroadcastController = initLiveStreamController();
    await muxBroadcastController!.create(
      initialAudioConfig: _initialAudioConfig,
      initialVideoConfig: _initialVideoConfig,
    );
    safeSetState(() => _isCameraInitialized = true);
  }

  LiveStreamController initLiveStreamController() {
    return LiveStreamController(
      onConnectionSuccess: () {
        print('Connection succeeded');
        safeSetState(() => muxBroadcastIsLive = true);
        _startTimer();
      },
      onConnectionFailed: (error) {
        print('Connection failed: $error');
        safeSetState(() {});
      },
      onDisconnection: () {
        print('Disconnected');
        safeSetState(() => muxBroadcastIsLive = false);
        _stopTimer();
      },
    );
  }

  Future<void> switchCamera() async {
    final LiveStreamController? liveStreamController = muxBroadcastController;
    if (liveStreamController == null) return;
    try {
      liveStreamController.switchCamera();
    } catch (error) {
      if (error is PlatformException) {
        print('Failed to switch camera: ${error.message}');
      } else {
        print('Failed to switch camera: $error');
      }
    }
  }

  Future<void> startStreaming() async {
    final LiveStreamController? liveStreamController = muxBroadcastController;
    if (liveStreamController == null) return;
    const streamBaseURL = 'rtmps://global-live.mux.com:443/app/';
    final callName = 'createLiveStream';
    final response =
        await makeCloudCall(callName, {'latency_mode': 'standard'});
    final streamKey = response['stream_key'];
    final playbackId = response['playback_ids'][0]['id'];
    muxBroadcastPlaybackUrl = 'https://stream.mux.com/$playbackId.m3u8';
    if (streamKey != null) {
      try {
        WakelockPlus.enable();
        await liveStreamController.startStreaming(
          streamKey: streamKey,
          url: streamBaseURL,
        );
      } catch (error) {
        if (error is PlatformException) {
          print("Error: failed to start stream: ${error.message}");
        } else {
          print("Error: failed to start stream: $error");
        }
      }
    }
  }

  Future<void> stopStreaming() async {
    final LiveStreamController? liveStreamController = muxBroadcastController;
    if (liveStreamController == null) return;
    try {
      WakelockPlus.disable();
      liveStreamController.stopStreaming();
      safeSetState(() => muxBroadcastIsLive = false);
      _stopTimer();
    } catch (error) {
      if (error is PlatformException) {
        print('Failed to stop stream: ${error.message}');
      } else {
        print('Failed to stop stream: $error');
      }
    }
  }

  void _startTimer() {
    _stopwatch.start();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      safeSetState(() {
        _durationString = _getDurationString(_stopwatch.elapsed);
      });
    });
  }

  void _stopTimer() {
    _stopwatch
      ..stop()
      ..reset();
    _durationString = _getDurationString(_stopwatch.elapsed);
    _timer?.cancel();
  }

  String _getDurationString(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}
