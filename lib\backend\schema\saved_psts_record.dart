import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class SavedPstsRecord extends FirestoreRecord {
  SavedPstsRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "savedPostID" field.
  DocumentReference? _savedPostID;
  DocumentReference? get savedPostID => _savedPostID;
  bool hasSavedPostID() => _savedPostID != null;

  // "savedPost" field.
  String? _savedPost;
  String get savedPost => _savedPost ?? '';
  bool hasSavedPost() => _savedPost != null;

  // "savedPhoto" field.
  String? _savedPhoto;
  String get savedPhoto => _savedPhoto ?? '';
  bool hasSavedPhoto() => _savedPhoto != null;

  // "savedTime" field.
  DateTime? _savedTime;
  DateTime? get savedTime => _savedTime;
  bool hasSavedTime() => _savedTime != null;

  // "savedPostDate" field.
  DateTime? _savedPostDate;
  DateTime? get savedPostDate => _savedPostDate;
  bool hasSavedPostDate() => _savedPostDate != null;

  // "savedPostlist" field.
  List<DocumentReference>? _savedPostlist;
  List<DocumentReference> get savedPostlist => _savedPostlist ?? const [];
  bool hasSavedPostlist() => _savedPostlist != null;

  // "saveBut" field.
  bool? _saveBut;
  bool get saveBut => _saveBut ?? false;
  bool hasSaveBut() => _saveBut != null;

  // "savedPostUser" field.
  DocumentReference? _savedPostUser;
  DocumentReference? get savedPostUser => _savedPostUser;
  bool hasSavedPostUser() => _savedPostUser != null;

  // "savedPostLike" field.
  List<DocumentReference>? _savedPostLike;
  List<DocumentReference> get savedPostLike => _savedPostLike ?? const [];
  bool hasSavedPostLike() => _savedPostLike != null;

  DocumentReference get parentReference => reference.parent.parent!;

  void _initializeFields() {
    _savedPostID = snapshotData['savedPostID'] as DocumentReference?;
    _savedPost = snapshotData['savedPost'] as String?;
    _savedPhoto = snapshotData['savedPhoto'] as String?;
    _savedTime = snapshotData['savedTime'] as DateTime?;
    _savedPostDate = snapshotData['savedPostDate'] as DateTime?;
    _savedPostlist = getDataList(snapshotData['savedPostlist']);
    _saveBut = snapshotData['saveBut'] as bool?;
    _savedPostUser = snapshotData['savedPostUser'] as DocumentReference?;
    _savedPostLike = getDataList(snapshotData['savedPostLike']);
  }

  static Query<Map<String, dynamic>> collection([DocumentReference? parent]) =>
      parent != null
          ? parent.collection('savedPsts')
          : FirebaseFirestore.instance.collectionGroup('savedPsts');

  static DocumentReference createDoc(DocumentReference parent, {String? id}) =>
      parent.collection('savedPsts').doc(id);

  static Stream<SavedPstsRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => SavedPstsRecord.fromSnapshot(s));

  static Future<SavedPstsRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => SavedPstsRecord.fromSnapshot(s));

  static SavedPstsRecord fromSnapshot(DocumentSnapshot snapshot) =>
      SavedPstsRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static SavedPstsRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      SavedPstsRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'SavedPstsRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is SavedPstsRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createSavedPstsRecordData({
  DocumentReference? savedPostID,
  String? savedPost,
  String? savedPhoto,
  DateTime? savedTime,
  DateTime? savedPostDate,
  bool? saveBut,
  DocumentReference? savedPostUser,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'savedPostID': savedPostID,
      'savedPost': savedPost,
      'savedPhoto': savedPhoto,
      'savedTime': savedTime,
      'savedPostDate': savedPostDate,
      'saveBut': saveBut,
      'savedPostUser': savedPostUser,
    }.withoutNulls,
  );

  return firestoreData;
}

class SavedPstsRecordDocumentEquality implements Equality<SavedPstsRecord> {
  const SavedPstsRecordDocumentEquality();

  @override
  bool equals(SavedPstsRecord? e1, SavedPstsRecord? e2) {
    const listEquality = ListEquality();
    return e1?.savedPostID == e2?.savedPostID &&
        e1?.savedPost == e2?.savedPost &&
        e1?.savedPhoto == e2?.savedPhoto &&
        e1?.savedTime == e2?.savedTime &&
        e1?.savedPostDate == e2?.savedPostDate &&
        listEquality.equals(e1?.savedPostlist, e2?.savedPostlist) &&
        e1?.saveBut == e2?.saveBut &&
        e1?.savedPostUser == e2?.savedPostUser &&
        listEquality.equals(e1?.savedPostLike, e2?.savedPostLike);
  }

  @override
  int hash(SavedPstsRecord? e) => const ListEquality().hash([
        e?.savedPostID,
        e?.savedPost,
        e?.savedPhoto,
        e?.savedTime,
        e?.savedPostDate,
        e?.savedPostlist,
        e?.saveBut,
        e?.savedPostUser,
        e?.savedPostLike
      ]);

  @override
  bool isValidKey(Object? o) => o is SavedPstsRecord;
}
