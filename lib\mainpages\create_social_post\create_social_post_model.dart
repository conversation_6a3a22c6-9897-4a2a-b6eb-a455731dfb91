import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'create_social_post_widget.dart' show CreateSocialPostWidget;
import 'package:flutter/material.dart';

class CreateSocialPostModel extends FlutterFlowModel<CreateSocialPostWidget> {
  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - Create Document] action in Button widget.
  PostRecord? out;
  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController1;
  String? Function(BuildContext, String?)? textController1Validator;
  // State field(s) for PostTitleFiled widget.
  FocusNode? postTitleFiledFocusNode;
  TextEditingController? postTitleFiledTextController;
  String? Function(BuildContext, String?)?
      postTitleFiledTextControllerValidator;
  bool isDataUploading_uploadDataKmk = false;
  FFUploadedFile uploadedLocalFile_uploadDataKmk =
      FFUploadedFile(bytes: Uint8List.fromList([]));
  String uploadedFileUrl_uploadDataKmk = '';

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    textFieldFocusNode?.dispose();
    textController1?.dispose();

    postTitleFiledFocusNode?.dispose();
    postTitleFiledTextController?.dispose();
  }
}
