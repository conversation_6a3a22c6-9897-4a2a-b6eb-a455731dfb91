import '/flutter_flow/flutter_flow_util.dart';
import 'audios_widget.dart' show AudiosWidget;
import 'package:flutter/material.dart';
import 'package:record/record.dart';

class AudiosModel extends FlutterFlowModel<AudiosWidget> {
  ///  State fields for stateful widgets in this page.

  AudioRecorder? audioRecorder;
  String? abc;
  FFUploadedFile recordedFileBytes =
      FFUploadedFile(bytes: Uint8List.fromList([]));

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
