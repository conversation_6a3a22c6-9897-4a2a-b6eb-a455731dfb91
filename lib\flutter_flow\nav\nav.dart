import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import '/backend/backend.dart';

import '/auth/base_auth_user_provider.dart';

import '/backend/push_notifications/push_notifications_handler.dart'
    show PushNotificationsHandler;
import '/main.dart';
import '/flutter_flow/flutter_flow_util.dart';

import '/index.dart';

export 'package:go_router/go_router.dart';
export 'serialization_util.dart';
export '/backend/firebase_dynamic_links/firebase_dynamic_links.dart'
    show generateCurrentPageLink;

const kTransitionInfoKey = '__transition_info__';

GlobalKey<NavigatorState> appNavigatorKey = GlobalKey<NavigatorState>();

class AppStateNotifier extends ChangeNotifier {
  AppStateNotifier._();

  static AppStateNotifier? _instance;
  static AppStateNotifier get instance => _instance ??= AppStateNotifier._();

  BaseAuthUser? initialUser;
  BaseAuthUser? user;
  bool showSplashImage = true;
  String? _redirectLocation;

  /// Determines whether the app will refresh and build again when a sign
  /// in or sign out happens. This is useful when the app is launched or
  /// on an unexpected logout. However, this must be turned off when we
  /// intend to sign in/out and then navigate or perform any actions after.
  /// Otherwise, this will trigger a refresh and interrupt the action(s).
  bool notifyOnAuthChange = true;

  bool get loading => user == null || showSplashImage;
  bool get loggedIn => user?.loggedIn ?? false;
  bool get initiallyLoggedIn => initialUser?.loggedIn ?? false;
  bool get shouldRedirect => loggedIn && _redirectLocation != null;

  String getRedirectLocation() => _redirectLocation!;
  bool hasRedirect() => _redirectLocation != null;
  void setRedirectLocationIfUnset(String loc) => _redirectLocation ??= loc;
  void clearRedirectLocation() => _redirectLocation = null;

  /// Mark as not needing to notify on a sign in / out when we intend
  /// to perform subsequent actions (such as navigation) afterwards.
  void updateNotifyOnAuthChange(bool notify) => notifyOnAuthChange = notify;

  void update(BaseAuthUser newUser) {
    final shouldUpdate =
        user?.uid == null || newUser.uid == null || user?.uid != newUser.uid;
    initialUser ??= newUser;
    user = newUser;
    // Refresh the app on auth change unless explicitly marked otherwise.
    // No need to update unless the user has changed.
    if (notifyOnAuthChange && shouldUpdate) {
      notifyListeners();
    }
    // Once again mark the notifier as needing to update on auth change
    // (in order to catch sign in / out events).
    updateNotifyOnAuthChange(true);
  }

  void stopShowingSplashImage() {
    showSplashImage = false;
    notifyListeners();
  }
}

GoRouter createRouter(AppStateNotifier appStateNotifier) => GoRouter(
      initialLocation: '/',
      debugLogDiagnostics: true,
      refreshListenable: appStateNotifier,
      navigatorKey: appNavigatorKey,
      errorBuilder: (context, state) => _RouteErrorBuilder(
        state: state,
        child: appStateNotifier.loggedIn ? NavBarPage() : Auth4WelcomeWidget(),
      ),
      routes: [
        FFRoute(
          name: '_initialize',
          path: '/',
          builder: (context, _) =>
              appStateNotifier.loggedIn ? NavBarPage() : Auth4WelcomeWidget(),
        ),
        FFRoute(
          name: CreateSocialPostWidget.routeName,
          path: CreateSocialPostWidget.routePath,
          requireAuth: true,
          builder: (context, params) => CreateSocialPostWidget(
            createrdisplayname: params.getParam(
              'createrdisplayname',
              ParamType.String,
            ),
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
            pageRef: params.getParam(
              'pageRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['pages'],
            ),
          ),
        ),
        FFRoute(
          name: DetailsReviewsWidget.routeName,
          path: DetailsReviewsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => DetailsReviewsWidget(),
        ),
        FFRoute(
          name: DetailsSocialPostWidget.routeName,
          path: DetailsSocialPostWidget.routePath,
          requireAuth: true,
          builder: (context, params) => DetailsSocialPostWidget(
            postdocref: params.getParam(
              'postdocref',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['users', 'post'],
            ),
          ),
        ),
        FFRoute(
          name: DetailsArticleDetailsWidget.routeName,
          path: DetailsArticleDetailsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => DetailsArticleDetailsWidget(
            postref: params.getParam(
              'postref',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: DetailsSupportFormWidget.routeName,
          path: DetailsSupportFormWidget.routePath,
          requireAuth: true,
          builder: (context, params) => DetailsSupportFormWidget(),
        ),
        FFRoute(
          name: ListUserSearchWidget.routeName,
          path: ListUserSearchWidget.routePath,
          requireAuth: true,
          builder: (context, params) => ListUserSearchWidget(),
        ),
        FFRoute(
          name: ListMessagesWidget.routeName,
          path: ListMessagesWidget.routePath,
          requireAuth: true,
          builder: (context, params) => ListMessagesWidget(),
        ),
        FFRoute(
            name: ProfileOtherUserWidget.routeName,
            path: ProfileOtherUserWidget.routePath,
            requireAuth: true,
            asyncParams: {
              'userprofile': getDoc(['users'], UsersRecord.fromSnapshot),
            },
            builder: (context, params) => NavBarPage(
                  initialPage: '',
                  page: ProfileOtherUserWidget(
                    userprofile: params.getParam(
                      'userprofile',
                      ParamType.Document,
                    ),
                  ),
                )),
        FFRoute(
          name: DetailsQuizPageWidget.routeName,
          path: DetailsQuizPageWidget.routePath,
          requireAuth: true,
          asyncParams: {
            'competitonRef':
                getDoc(['compettion'], CompettionRecord.fromSnapshot),
          },
          builder: (context, params) => DetailsQuizPageWidget(
            competitonRef: params.getParam(
              'competitonRef',
              ParamType.Document,
            ),
          ),
        ),
        FFRoute(
          name: ListNotificationsWidget.routeName,
          path: ListNotificationsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => ListNotificationsWidget(),
        ),
        FFRoute(
          name: SettingsPageWidget.routeName,
          path: SettingsPageWidget.routePath,
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? NavBarPage(initialPage: 'SettingsPage')
              : SettingsPageWidget(),
        ),
        FFRoute(
          name: HomePageWidget.routeName,
          path: HomePageWidget.routePath,
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? NavBarPage(initialPage: 'HomePage')
              : HomePageWidget(),
        ),
        FFRoute(
          name: ProfileWidget.routeName,
          path: ProfileWidget.routePath,
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? NavBarPage(initialPage: 'profile')
              : ProfileWidget(),
        ),
        FFRoute(
          name: StreamsWidget.routeName,
          path: StreamsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => StreamsWidget(),
        ),
        FFRoute(
          name: LivestreamerWidget.routeName,
          path: LivestreamerWidget.routePath,
          requireAuth: true,
          builder: (context, params) => LivestreamerWidget(
            streamName: params.getParam(
              'streamName',
              ParamType.String,
            ),
            streamRef: params.getParam(
              'streamRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['streams'],
            ),
          ),
        ),
        FFRoute(
          name: LivestreamViewerWidget.routeName,
          path: LivestreamViewerWidget.routePath,
          requireAuth: true,
          builder: (context, params) => LivestreamViewerWidget(
            url: params.getParam(
              'url',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
            name: YoutubeplayerWidget.routeName,
            path: YoutubeplayerWidget.routePath,
            requireAuth: true,
            builder: (context, params) => NavBarPage(
                  initialPage: '',
                  page: YoutubeplayerWidget(),
                )),
        FFRoute(
          name: PostWidget.routeName,
          path: PostWidget.routePath,
          requireAuth: true,
          builder: (context, params) => PostWidget(),
        ),
        FFRoute(
          name: Chat2DetailsWidget.routeName,
          path: Chat2DetailsWidget.routePath,
          requireAuth: true,
          asyncParams: {
            'chatRef': getDoc(['chats'], ChatsRecord.fromSnapshot),
          },
          builder: (context, params) => Chat2DetailsWidget(
            chatRef: params.getParam(
              'chatRef',
              ParamType.Document,
            ),
          ),
        ),
        FFRoute(
          name: Chat2MainWidget.routeName,
          path: Chat2MainWidget.routePath,
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? NavBarPage(initialPage: 'chat_2_main')
              : Chat2MainWidget(),
        ),
        FFRoute(
          name: Chat2InviteUsersWidget.routeName,
          path: Chat2InviteUsersWidget.routePath,
          requireAuth: true,
          asyncParams: {
            'chatRef': getDoc(['chats'], ChatsRecord.fromSnapshot),
          },
          builder: (context, params) => Chat2InviteUsersWidget(
            chatRef: params.getParam(
              'chatRef',
              ParamType.Document,
            ),
          ),
        ),
        FFRoute(
          name: ImageDetailsWidget.routeName,
          path: ImageDetailsWidget.routePath,
          requireAuth: true,
          asyncParams: {
            'chatMessage':
                getDoc(['chat_messages'], ChatMessagesRecord.fromSnapshot),
          },
          builder: (context, params) => ImageDetailsWidget(
            chatMessage: params.getParam(
              'chatMessage',
              ParamType.Document,
            ),
          ),
        ),
        FFRoute(
          name: NotificationsListWidget.routeName,
          path: NotificationsListWidget.routePath,
          requireAuth: true,
          builder: (context, params) => NotificationsListWidget(),
        ),
        FFRoute(
          name: NotificationCreateWidget.routeName,
          path: NotificationCreateWidget.routePath,
          requireAuth: true,
          builder: (context, params) => NotificationCreateWidget(),
        ),
        FFRoute(
          name: CreateCommentWidget.routeName,
          path: CreateCommentWidget.routePath,
          requireAuth: true,
          builder: (context, params) => CreateCommentWidget(
            postdocref: params.getParam(
              'postdocref',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['users', 'post'],
            ),
          ),
        ),
        FFRoute(
          name: Auth2CreateWidget.routeName,
          path: Auth2CreateWidget.routePath,
          builder: (context, params) => Auth2CreateWidget(),
        ),
        FFRoute(
            name: Auth2LoginWidget.routeName,
            path: Auth2LoginWidget.routePath,
            builder: (context, params) => NavBarPage(
                  initialPage: '',
                  page: Auth2LoginWidget(),
                )),
        FFRoute(
          name: Auth2ForgotPasswordWidget.routeName,
          path: Auth2ForgotPasswordWidget.routePath,
          builder: (context, params) => Auth2ForgotPasswordWidget(),
        ),
        FFRoute(
          name: Auth2CreateProfileWidget.routeName,
          path: Auth2CreateProfileWidget.routePath,
          requireAuth: true,
          builder: (context, params) => Auth2CreateProfileWidget(),
        ),
        FFRoute(
          name: Auth2ProfileWidget.routeName,
          path: Auth2ProfileWidget.routePath,
          requireAuth: true,
          builder: (context, params) => Auth2ProfileWidget(),
        ),
        FFRoute(
          name: Auth2EditProfileWidget.routeName,
          path: Auth2EditProfileWidget.routePath,
          requireAuth: true,
          builder: (context, params) => Auth2EditProfileWidget(),
        ),
        FFRoute(
          name: AbcWidget.routeName,
          path: AbcWidget.routePath,
          requireAuth: true,
          builder: (context, params) => AbcWidget(),
        ),
        FFRoute(
          name: AudiosWidget.routeName,
          path: AudiosWidget.routePath,
          requireAuth: true,
          builder: (context, params) => AudiosWidget(),
        ),
        FFRoute(
          name: CreatePagesWidget.routeName,
          path: CreatePagesWidget.routePath,
          requireAuth: true,
          builder: (context, params) => CreatePagesWidget(),
        ),
        FFRoute(
          name: PageVIewWidget.routeName,
          path: PageVIewWidget.routePath,
          requireAuth: true,
          builder: (context, params) => PageVIewWidget(
            pageRef: params.getParam(
              'pageRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['pages'],
            ),
          ),
        ),
        FFRoute(
            name: AllPagesWidget.routeName,
            path: AllPagesWidget.routePath,
            requireAuth: true,
            builder: (context, params) => NavBarPage(
                  initialPage: '',
                  page: AllPagesWidget(),
                )),
        FFRoute(
          name: FollowersWidget.routeName,
          path: FollowersWidget.routePath,
          requireAuth: true,
          builder: (context, params) => FollowersWidget(
            userRef: params.getParam(
              'userRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['users'],
            ),
          ),
        ),
        FFRoute(
          name: FollowingWidget.routeName,
          path: FollowingWidget.routePath,
          requireAuth: true,
          builder: (context, params) => FollowingWidget(
            userRef: params.getParam(
              'userRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['users'],
            ),
          ),
        ),
        FFRoute(
          name: MyNotificationsWidget.routeName,
          path: MyNotificationsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => MyNotificationsWidget(),
        ),
        FFRoute(
          name: CreateGroupsWidget.routeName,
          path: CreateGroupsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => CreateGroupsWidget(),
        ),
        FFRoute(
          name: CreateGroupRulesWidget.routeName,
          path: CreateGroupRulesWidget.routePath,
          requireAuth: true,
          builder: (context, params) => CreateGroupRulesWidget(
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
          ),
        ),
        FFRoute(
          name: AddgroupMembersWidget.routeName,
          path: AddgroupMembersWidget.routePath,
          requireAuth: true,
          builder: (context, params) => AddgroupMembersWidget(
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
          ),
        ),
        FFRoute(
          name: GroupviewWidget.routeName,
          path: GroupviewWidget.routePath,
          requireAuth: true,
          builder: (context, params) => GroupviewWidget(
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
          ),
        ),
        FFRoute(
          name: Auth4OnboardingOneWidget.routeName,
          path: Auth4OnboardingOneWidget.routePath,
          builder: (context, params) => Auth4OnboardingOneWidget(
            index: params.getParam(
              'index',
              ParamType.int,
            ),
          ),
        ),
        FFRoute(
          name: Auth4WelcomeWidget.routeName,
          path: Auth4WelcomeWidget.routePath,
          builder: (context, params) => Auth4WelcomeWidget(),
        ),
        FFRoute(
          name: Auth4OnboardingPhoneVerifyWidget.routeName,
          path: Auth4OnboardingPhoneVerifyWidget.routePath,
          builder: (context, params) => Auth4OnboardingPhoneVerifyWidget(
            phoneNumber: params.getParam(
              'phoneNumber',
              ParamType.String,
            ),
            isLogin: params.getParam(
              'isLogin',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: Auth4LoginWidget.routeName,
          path: Auth4LoginWidget.routePath,
          builder: (context, params) => Auth4LoginWidget(),
        ),
        FFRoute(
          name: GroupJoinWidget.routeName,
          path: GroupJoinWidget.routePath,
          requireAuth: true,
          builder: (context, params) => GroupJoinWidget(
            index: params.getParam(
              'index',
              ParamType.int,
            ),
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
          ),
        ),
        FFRoute(
          name: GroupRulesWidget.routeName,
          path: GroupRulesWidget.routePath,
          requireAuth: true,
          builder: (context, params) => GroupRulesWidget(
            index: params.getParam(
              'index',
              ParamType.int,
            ),
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
          ),
        ),
        FFRoute(
          name: AdminPageWidget.routeName,
          path: AdminPageWidget.routePath,
          requireAuth: true,
          builder: (context, params) => AdminPageWidget(
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
            userRef: params.getParam(
              'userRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['users'],
            ),
          ),
        ),
        FFRoute(
          name: ActivityCreateWidget.routeName,
          path: ActivityCreateWidget.routePath,
          requireAuth: true,
          builder: (context, params) => ActivityCreateWidget(),
        ),
        FFRoute(
          name: AdminPanelWidget.routeName,
          path: AdminPanelWidget.routePath,
          requireAuth: true,
          builder: (context, params) => AdminPanelWidget(
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
            whichPage: params.getParam(
              'whichPage',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: EditGroupRulesWidget.routeName,
          path: EditGroupRulesWidget.routePath,
          requireAuth: true,
          builder: (context, params) => EditGroupRulesWidget(
            groupRef: params.getParam(
              'groupRef',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['groups'],
            ),
          ),
        ),
        FFRoute(
            name: SearchWidget.routeName,
            path: SearchWidget.routePath,
            requireAuth: true,
            builder: (context, params) => params.isEmpty
                ? NavBarPage(initialPage: 'search')
                : NavBarPage(
                    initialPage: 'search',
                    page: SearchWidget(),
                  )),
        FFRoute(
          name: AllgroupsWidget.routeName,
          path: AllgroupsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => AllgroupsWidget(),
        ),
        FFRoute(
          name: CreateCompetitionWidget.routeName,
          path: CreateCompetitionWidget.routePath,
          requireAuth: true,
          builder: (context, params) => CreateCompetitionWidget(),
        ),
        FFRoute(
          name: AllCompetitonsWidget.routeName,
          path: AllCompetitonsWidget.routePath,
          requireAuth: true,
          builder: (context, params) => AllCompetitonsWidget(),
        ),
        FFRoute(
          name: MyCOmpetionWidget.routeName,
          path: MyCOmpetionWidget.routePath,
          requireAuth: true,
          builder: (context, params) => MyCOmpetionWidget(),
        ),
        FFRoute(
          name: AboutusWidget.routeName,
          path: AboutusWidget.routePath,
          builder: (context, params) => AboutusWidget(),
        ),
        FFRoute(
          name: TermsConditionsWidget.routeName,
          path: TermsConditionsWidget.routePath,
          builder: (context, params) => TermsConditionsWidget(),
        )
      ].map((r) => r.toRoute(appStateNotifier)).toList(),
      observers: [routeObserver],
    );

extension NavParamExtensions on Map<String, String?> {
  Map<String, String> get withoutNulls => Map.fromEntries(
        entries
            .where((e) => e.value != null)
            .map((e) => MapEntry(e.key, e.value!)),
      );
}

extension NavigationExtensions on BuildContext {
  void goNamedAuth(
    String name,
    bool mounted, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, String> queryParameters = const <String, String>{},
    Object? extra,
    bool ignoreRedirect = false,
  }) =>
      !mounted || GoRouter.of(this).shouldRedirect(ignoreRedirect)
          ? null
          : goNamed(
              name,
              pathParameters: pathParameters,
              queryParameters: queryParameters,
              extra: extra,
            );

  void pushNamedAuth(
    String name,
    bool mounted, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, String> queryParameters = const <String, String>{},
    Object? extra,
    bool ignoreRedirect = false,
  }) =>
      !mounted || GoRouter.of(this).shouldRedirect(ignoreRedirect)
          ? null
          : pushNamed(
              name,
              pathParameters: pathParameters,
              queryParameters: queryParameters,
              extra: extra,
            );

  void safePop() {
    // If there is only one route on the stack, navigate to the initial
    // page instead of popping.
    if (canPop()) {
      pop();
    } else {
      go('/');
    }
  }
}

extension GoRouterExtensions on GoRouter {
  AppStateNotifier get appState => AppStateNotifier.instance;
  void prepareAuthEvent([bool ignoreRedirect = false]) =>
      appState.hasRedirect() && !ignoreRedirect
          ? null
          : appState.updateNotifyOnAuthChange(false);
  bool shouldRedirect(bool ignoreRedirect) =>
      !ignoreRedirect && appState.hasRedirect();
  void clearRedirectLocation() => appState.clearRedirectLocation();
  void setRedirectLocationIfUnset(String location) =>
      appState.updateNotifyOnAuthChange(false);
}

extension _GoRouterStateExtensions on GoRouterState {
  Map<String, dynamic> get extraMap =>
      extra != null ? extra as Map<String, dynamic> : {};
  Map<String, dynamic> get allParams => <String, dynamic>{}
    ..addAll(pathParameters)
    ..addAll(uri.queryParameters)
    ..addAll(extraMap);
  TransitionInfo get transitionInfo => extraMap.containsKey(kTransitionInfoKey)
      ? extraMap[kTransitionInfoKey] as TransitionInfo
      : TransitionInfo.appDefault();
}

class FFParameters {
  FFParameters(this.state, [this.asyncParams = const {}]);

  final GoRouterState state;
  final Map<String, Future<dynamic> Function(String)> asyncParams;

  Map<String, dynamic> futureParamValues = {};

  // Parameters are empty if the params map is empty or if the only parameter
  // present is the special extra parameter reserved for the transition info.
  bool get isEmpty =>
      state.allParams.isEmpty ||
      (state.allParams.length == 1 &&
          state.extraMap.containsKey(kTransitionInfoKey));
  bool isAsyncParam(MapEntry<String, dynamic> param) =>
      asyncParams.containsKey(param.key) && param.value is String;
  bool get hasFutures => state.allParams.entries.any(isAsyncParam);
  Future<bool> completeFutures() => Future.wait(
        state.allParams.entries.where(isAsyncParam).map(
          (param) async {
            final doc = await asyncParams[param.key]!(param.value)
                .onError((_, __) => null);
            if (doc != null) {
              futureParamValues[param.key] = doc;
              return true;
            }
            return false;
          },
        ),
      ).onError((_, __) => [false]).then((v) => v.every((e) => e));

  dynamic getParam<T>(
    String paramName,
    ParamType type, {
    bool isList = false,
    List<String>? collectionNamePath,
    StructBuilder<T>? structBuilder,
  }) {
    if (futureParamValues.containsKey(paramName)) {
      return futureParamValues[paramName];
    }
    if (!state.allParams.containsKey(paramName)) {
      return null;
    }
    final param = state.allParams[paramName];
    // Got parameter from `extras`, so just directly return it.
    if (param is! String) {
      return param;
    }
    // Return serialized value.
    return deserializeParam<T>(
      param,
      type,
      isList,
      collectionNamePath: collectionNamePath,
      structBuilder: structBuilder,
    );
  }
}

class FFRoute {
  const FFRoute({
    required this.name,
    required this.path,
    required this.builder,
    this.requireAuth = false,
    this.asyncParams = const {},
    this.routes = const [],
  });

  final String name;
  final String path;
  final bool requireAuth;
  final Map<String, Future<dynamic> Function(String)> asyncParams;
  final Widget Function(BuildContext, FFParameters) builder;
  final List<GoRoute> routes;

  GoRoute toRoute(AppStateNotifier appStateNotifier) => GoRoute(
        name: name,
        path: path,
        redirect: (context, state) {
          if (appStateNotifier.shouldRedirect) {
            final redirectLocation = appStateNotifier.getRedirectLocation();
            appStateNotifier.clearRedirectLocation();
            return redirectLocation;
          }

          if (requireAuth && !appStateNotifier.loggedIn) {
            appStateNotifier.setRedirectLocationIfUnset(state.uri.toString());
            return '/auth4Welcome';
          }
          return null;
        },
        pageBuilder: (context, state) {
          fixStatusBarOniOS16AndBelow(context);
          final ffParams = FFParameters(state, asyncParams);
          final page = ffParams.hasFutures
              ? FutureBuilder(
                  future: ffParams.completeFutures(),
                  builder: (context, _) => builder(context, ffParams),
                )
              : builder(context, ffParams);
          final child = appStateNotifier.loading
              ? Container(
                  color: Colors.transparent,
                  child: Image.asset(
                    'assets/images/Join_Us.png',
                    fit: BoxFit.cover,
                  ),
                )
              : PushNotificationsHandler(child: page);

          final transitionInfo = state.transitionInfo;
          return transitionInfo.hasTransition
              ? CustomTransitionPage(
                  key: state.pageKey,
                  child: child,
                  transitionDuration: transitionInfo.duration,
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) =>
                          PageTransition(
                    type: transitionInfo.transitionType,
                    duration: transitionInfo.duration,
                    reverseDuration: transitionInfo.duration,
                    alignment: transitionInfo.alignment,
                    child: child,
                  ).buildTransitions(
                    context,
                    animation,
                    secondaryAnimation,
                    child,
                  ),
                )
              : MaterialPage(key: state.pageKey, child: child);
        },
        routes: routes,
      );
}

class TransitionInfo {
  const TransitionInfo({
    required this.hasTransition,
    this.transitionType = PageTransitionType.fade,
    this.duration = const Duration(milliseconds: 300),
    this.alignment,
  });

  final bool hasTransition;
  final PageTransitionType transitionType;
  final Duration duration;
  final Alignment? alignment;

  static TransitionInfo appDefault() => TransitionInfo(hasTransition: false);
}

class _RouteErrorBuilder extends StatefulWidget {
  const _RouteErrorBuilder({
    Key? key,
    required this.state,
    required this.child,
  }) : super(key: key);

  final GoRouterState state;
  final Widget child;

  @override
  State<_RouteErrorBuilder> createState() => _RouteErrorBuilderState();
}

class _RouteErrorBuilderState extends State<_RouteErrorBuilder> {
  @override
  void initState() {
    super.initState();

    // Handle erroneous links from Firebase Dynamic Links.

    String? location;

    /*
    Handle `links` routes that have dynamic-link entangled with deep-link 
    */
    if (widget.state.uri.toString().startsWith('/link') &&
        widget.state.uri.queryParameters.containsKey('deep_link_id')) {
      final deepLinkId = widget.state.uri.queryParameters['deep_link_id'];
      if (deepLinkId != null) {
        final deepLinkUri = Uri.parse(deepLinkId);
        final link = deepLinkUri.toString();
        final host = deepLinkUri.host;
        location = link.split(host).last;
      }
    }

    if (widget.state.uri.toString().startsWith('/link') &&
        widget.state.uri.toString().contains('request_ip_version')) {
      location = '/';
    }

    if (location != null) {
      SchedulerBinding.instance
          .addPostFrameCallback((_) => context.go(location!));
    }
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

class RootPageContext {
  const RootPageContext(this.isRootPage, [this.errorRoute]);
  final bool isRootPage;
  final String? errorRoute;

  static bool isInactiveRootPage(BuildContext context) {
    final rootPageContext = context.read<RootPageContext?>();
    final isRootPage = rootPageContext?.isRootPage ?? false;
    final location = GoRouterState.of(context).uri.toString();
    return isRootPage &&
        location != '/' &&
        location != rootPageContext?.errorRoute;
  }

  static Widget wrap(Widget child, {String? errorRoute}) => Provider.value(
        value: RootPageContext(true, errorRoute),
        child: child,
      );
}

extension GoRouterLocationExtension on GoRouter {
  String getCurrentLocation() {
    final RouteMatch lastMatch = routerDelegate.currentConfiguration.last;
    final RouteMatchList matchList = lastMatch is ImperativeRouteMatch
        ? lastMatch.matches
        : routerDelegate.currentConfiguration;
    return matchList.uri.toString();
  }
}
