import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class SearchRecord extends FirestoreRecord {
  SearchRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "searched" field.
  String? _searched;
  String get searched => _searched ?? '';
  bool hasSearched() => _searched != null;

  // "searchedTime" field.
  DateTime? _searchedTime;
  DateTime? get searchedTime => _searchedTime;
  bool hasSearchedTime() => _searchedTime != null;

  DocumentReference get parentReference => reference.parent.parent!;

  void _initializeFields() {
    _searched = snapshotData['searched'] as String?;
    _searchedTime = snapshotData['searchedTime'] as DateTime?;
  }

  static Query<Map<String, dynamic>> collection([DocumentReference? parent]) =>
      parent != null
          ? parent.collection('search')
          : FirebaseFirestore.instance.collectionGroup('search');

  static DocumentReference createDoc(DocumentReference parent, {String? id}) =>
      parent.collection('search').doc(id);

  static Stream<SearchRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => SearchRecord.fromSnapshot(s));

  static Future<SearchRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => SearchRecord.fromSnapshot(s));

  static SearchRecord fromSnapshot(DocumentSnapshot snapshot) => SearchRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static SearchRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      SearchRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'SearchRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is SearchRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createSearchRecordData({
  String? searched,
  DateTime? searchedTime,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'searched': searched,
      'searchedTime': searchedTime,
    }.withoutNulls,
  );

  return firestoreData;
}

class SearchRecordDocumentEquality implements Equality<SearchRecord> {
  const SearchRecordDocumentEquality();

  @override
  bool equals(SearchRecord? e1, SearchRecord? e2) {
    return e1?.searched == e2?.searched && e1?.searchedTime == e2?.searchedTime;
  }

  @override
  int hash(SearchRecord? e) =>
      const ListEquality().hash([e?.searched, e?.searchedTime]);

  @override
  bool isValidKey(Object? o) => o is SearchRecord;
}
