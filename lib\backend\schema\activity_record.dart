import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class ActivityRecord extends FirestoreRecord {
  ActivityRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "name" field.
  String? _name;
  String get name => _name ?? '';
  bool hasName() => _name != null;

  // "description" field.
  String? _description;
  String get description => _description ?? '';
  bool hasDescription() => _description != null;

  // "type" field.
  String? _type;
  String get type => _type ?? '';
  bool hasType() => _type != null;

  // "timePosted" field.
  DateTime? _timePosted;
  DateTime? get timePosted => _timePosted;
  bool hasTimePosted() => _timePosted != null;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  bool hasImage() => _image != null;

  // "notifyUsers" field.
  List<DocumentReference>? _notifyUsers;
  List<DocumentReference> get notifyUsers => _notifyUsers ?? const [];
  bool hasNotifyUsers() => _notifyUsers != null;

  // "owner" field.
  DocumentReference? _owner;
  DocumentReference? get owner => _owner;
  bool hasOwner() => _owner != null;

  // "group_ref" field.
  DocumentReference? _groupRef;
  DocumentReference? get groupRef => _groupRef;
  bool hasGroupRef() => _groupRef != null;

  // "activity_ref" field.
  DocumentReference? _activityRef;
  DocumentReference? get activityRef => _activityRef;
  bool hasActivityRef() => _activityRef != null;

  // "stream_ref" field.
  DocumentReference? _streamRef;
  DocumentReference? get streamRef => _streamRef;
  bool hasStreamRef() => _streamRef != null;

  // "post_ref" field.
  DocumentReference? _postRef;
  DocumentReference? get postRef => _postRef;
  bool hasPostRef() => _postRef != null;

  // "group_bool" field.
  bool? _groupBool;
  bool get groupBool => _groupBool ?? false;
  bool hasGroupBool() => _groupBool != null;

  // "stream_bool" field.
  bool? _streamBool;
  bool get streamBool => _streamBool ?? false;
  bool hasStreamBool() => _streamBool != null;

  void _initializeFields() {
    _name = snapshotData['name'] as String?;
    _description = snapshotData['description'] as String?;
    _type = snapshotData['type'] as String?;
    _timePosted = snapshotData['timePosted'] as DateTime?;
    _image = snapshotData['image'] as String?;
    _notifyUsers = getDataList(snapshotData['notifyUsers']);
    _owner = snapshotData['owner'] as DocumentReference?;
    _groupRef = snapshotData['group_ref'] as DocumentReference?;
    _activityRef = snapshotData['activity_ref'] as DocumentReference?;
    _streamRef = snapshotData['stream_ref'] as DocumentReference?;
    _postRef = snapshotData['post_ref'] as DocumentReference?;
    _groupBool = snapshotData['group_bool'] as bool?;
    _streamBool = snapshotData['stream_bool'] as bool?;
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('activity');

  static Stream<ActivityRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => ActivityRecord.fromSnapshot(s));

  static Future<ActivityRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => ActivityRecord.fromSnapshot(s));

  static ActivityRecord fromSnapshot(DocumentSnapshot snapshot) =>
      ActivityRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static ActivityRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      ActivityRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'ActivityRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is ActivityRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createActivityRecordData({
  String? name,
  String? description,
  String? type,
  DateTime? timePosted,
  String? image,
  DocumentReference? owner,
  DocumentReference? groupRef,
  DocumentReference? activityRef,
  DocumentReference? streamRef,
  DocumentReference? postRef,
  bool? groupBool,
  bool? streamBool,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'name': name,
      'description': description,
      'type': type,
      'timePosted': timePosted,
      'image': image,
      'owner': owner,
      'group_ref': groupRef,
      'activity_ref': activityRef,
      'stream_ref': streamRef,
      'post_ref': postRef,
      'group_bool': groupBool,
      'stream_bool': streamBool,
    }.withoutNulls,
  );

  return firestoreData;
}

class ActivityRecordDocumentEquality implements Equality<ActivityRecord> {
  const ActivityRecordDocumentEquality();

  @override
  bool equals(ActivityRecord? e1, ActivityRecord? e2) {
    const listEquality = ListEquality();
    return e1?.name == e2?.name &&
        e1?.description == e2?.description &&
        e1?.type == e2?.type &&
        e1?.timePosted == e2?.timePosted &&
        e1?.image == e2?.image &&
        listEquality.equals(e1?.notifyUsers, e2?.notifyUsers) &&
        e1?.owner == e2?.owner &&
        e1?.groupRef == e2?.groupRef &&
        e1?.activityRef == e2?.activityRef &&
        e1?.streamRef == e2?.streamRef &&
        e1?.postRef == e2?.postRef &&
        e1?.groupBool == e2?.groupBool &&
        e1?.streamBool == e2?.streamBool;
  }

  @override
  int hash(ActivityRecord? e) => const ListEquality().hash([
        e?.name,
        e?.description,
        e?.type,
        e?.timePosted,
        e?.image,
        e?.notifyUsers,
        e?.owner,
        e?.groupRef,
        e?.activityRef,
        e?.streamRef,
        e?.postRef,
        e?.groupBool,
        e?.streamBool
      ]);

  @override
  bool isValidKey(Object? o) => o is ActivityRecord;
}
