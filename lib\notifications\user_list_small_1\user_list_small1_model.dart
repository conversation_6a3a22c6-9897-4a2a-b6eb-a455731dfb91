import '/flutter_flow/flutter_flow_util.dart';
import 'user_list_small1_widget.dart' show UserListSmall1Widget;
import 'package:flutter/material.dart';

class UserListSmall1Model extends FlutterFlowModel<UserListSmall1Widget> {
  ///  State fields for stateful widgets in this component.

  // State field(s) for iuser widget.
  bool iuserHovered = false;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
