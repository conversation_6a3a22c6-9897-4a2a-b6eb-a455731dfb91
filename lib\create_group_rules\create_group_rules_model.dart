import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'create_group_rules_widget.dart' show CreateGroupRulesWidget;
import 'package:flutter/material.dart';

class CreateGroupRulesModel extends FlutterFlowModel<CreateGroupRulesWidget> {
  ///  Local state fields for this page.

  List<String> hastagsList = [];
  void addToHastagsList(String item) => hastagsList.add(item);
  void removeFromHastagsList(String item) => hastagsList.remove(item);
  void removeAtIndexFromHastagsList(int index) => hastagsList.removeAt(index);
  void insertAtIndexInHastagsList(int index, String item) =>
      hastagsList.insert(index, item);
  void updateHastagsListAtIndex(int index, Function(String) updateFn) =>
      hastagsList[index] = updateFn(hastagsList[index]);

  bool number = false;

  bool email = false;

  bool name = false;

  bool request = false;

  ///  State fields for stateful widgets in this page.

  final formKey = GlobalKey<FormState>();
  // State field(s) for description widget.
  FocusNode? descriptionFocusNode;
  TextEditingController? descriptionTextController;
  String? Function(BuildContext, String?)? descriptionTextControllerValidator;
  // State field(s) for CheckboxName widget.
  bool? checkboxNameValue;
  // State field(s) for CheckboxMail widget.
  bool? checkboxMailValue;
  // State field(s) for CheckboxPH widget.
  bool? checkboxPHValue1;
  // State field(s) for CheckboxPH widget.
  bool? checkboxPHValue2;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    descriptionFocusNode?.dispose();
    descriptionTextController?.dispose();
  }
}
