import '/backend/api_requests/api_calls.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'streams_widget.dart' show StreamsWidget;
import 'package:flutter/material.dart';

class StreamsModel extends FlutterFlowModel<StreamsWidget> {
  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - API (getLivestreamID)] action in Container widget.
  ApiCallResponse? livestreamID;
  // Stores action output result for [Backend Call - API (getPastLiveStream)] action in Container widget.
  ApiCallResponse? apiResultwe1;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
