import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class TypeRecord extends FirestoreRecord {
  TypeRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "typesRef" field.
  List<String>? _typesRef;
  List<String> get typesRef => _typesRef ?? const [];
  bool hasTypesRef() => _typesRef != null;

  // "typeRef" field.
  String? _typeRef;
  String get typeRef => _typeRef ?? '';
  bool hasTypeRef() => _typeRef != null;

  void _initializeFields() {
    _typesRef = getDataList(snapshotData['typesRef']);
    _typeRef = snapshotData['typeRef'] as String?;
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('type');

  static Stream<TypeRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => TypeRecord.fromSnapshot(s));

  static Future<TypeRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => TypeRecord.fromSnapshot(s));

  static TypeRecord fromSnapshot(DocumentSnapshot snapshot) => TypeRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static TypeRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      TypeRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'TypeRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is TypeRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createTypeRecordData({
  String? typeRef,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'typeRef': typeRef,
    }.withoutNulls,
  );

  return firestoreData;
}

class TypeRecordDocumentEquality implements Equality<TypeRecord> {
  const TypeRecordDocumentEquality();

  @override
  bool equals(TypeRecord? e1, TypeRecord? e2) {
    const listEquality = ListEquality();
    return listEquality.equals(e1?.typesRef, e2?.typesRef) &&
        e1?.typeRef == e2?.typeRef;
  }

  @override
  int hash(TypeRecord? e) =>
      const ListEquality().hash([e?.typesRef, e?.typeRef]);

  @override
  bool isValidKey(Object? o) => o is TypeRecord;
}
