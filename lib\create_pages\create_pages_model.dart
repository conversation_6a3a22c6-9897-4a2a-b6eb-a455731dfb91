import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import '/index.dart';
import 'create_pages_widget.dart' show CreatePagesWidget;
import 'package:flutter/material.dart';

class CreatePagesModel extends FlutterFlowModel<CreatePagesWidget> {
  ///  Local state fields for this page.

  List<String> hastagsList = [];
  void addToHastagsList(String item) => hastagsList.add(item);
  void removeFromHastagsList(String item) => hastagsList.remove(item);
  void removeAtIndexFromHastagsList(int index) => hastagsList.removeAt(index);
  void insertAtIndexInHastagsList(int index, String item) =>
      hastagsList.insert(index, item);
  void updateHastagsListAtIndex(int index, Function(String) updateFn) =>
      hastagsList[index] = updateFn(hastagsList[index]);

  ///  State fields for stateful widgets in this page.

  final formKey = GlobalKey<FormState>();
  bool isDataUploading_uploadDatapage = false;
  FFUploadedFile uploadedLocalFile_uploadDatapage =
      FFUploadedFile(bytes: Uint8List.fromList([]));
  String uploadedFileUrl_uploadDatapage = '';

  // State field(s) for productName widget.
  FocusNode? productNameFocusNode;
  TextEditingController? productNameTextController;
  String? Function(BuildContext, String?)? productNameTextControllerValidator;
  // State field(s) for description widget.
  FocusNode? descriptionFocusNode;
  TextEditingController? descriptionTextController;
  String? Function(BuildContext, String?)? descriptionTextControllerValidator;
  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController3;
  String? Function(BuildContext, String?)? textController3Validator;
  // State field(s) for ChoiceChips widget.
  FormFieldController<List<String>>? choiceChipsValueController;
  String? get choiceChipsValue =>
      choiceChipsValueController?.value?.firstOrNull;
  set choiceChipsValue(String? val) =>
      choiceChipsValueController?.value = val != null ? [val] : [];

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    productNameFocusNode?.dispose();
    productNameTextController?.dispose();

    descriptionFocusNode?.dispose();
    descriptionTextController?.dispose();

    textFieldFocusNode?.dispose();
    textController3?.dispose();
  }
}
