import '/flutter_flow/flutter_flow_util.dart';
import 'delete_dialog_widget.dart' show DeleteDialogWidget;
import 'package:flutter/material.dart';

class DeleteDialogModel extends FlutterFlowModel<DeleteDialogWidget> {
  ///  Local state fields for this component.

  bool showDelete = false;

  ///  State fields for stateful widgets in this component.

  // State field(s) for MouseRegion widget.
  bool mouseRegionHovered1 = false;
  // State field(s) for MouseRegion widget.
  bool mouseRegionHovered2 = false;
  // State field(s) for MouseRegion widget.
  bool mouseRegionHovered3 = false;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
