import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'com_model.dart';
export 'com_model.dart';

class ComWidget extends StatefulWidget {
  const ComWidget({super.key});

  @override
  State<ComWidget> createState() => _ComWidgetState();
}

class _ComWidgetState extends State<ComWidget> {
  late ComModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ComModel());

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: 10.0,
      decoration: BoxDecoration(),
    );
  }
}
