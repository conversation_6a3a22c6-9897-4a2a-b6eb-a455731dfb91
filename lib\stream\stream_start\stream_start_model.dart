import '/flutter_flow/flutter_flow_util.dart';
import 'stream_start_widget.dart' show StreamStartWidget;
import 'package:flutter/material.dart';

class StreamStartModel extends FlutterFlowModel<StreamStartWidget> {
  ///  State fields for stateful widgets in this component.

  // State field(s) for streamName widget.
  FocusNode? streamNameFocusNode;
  TextEditingController? streamNameTextController;
  String? Function(BuildContext, String?)? streamNameTextControllerValidator;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    streamNameFocusNode?.dispose();
    streamNameTextController?.dispose();
  }
}
