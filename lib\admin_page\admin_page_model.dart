import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'admin_page_widget.dart' show AdminPageWidget;
import 'package:flutter/material.dart';

class AdminPageModel extends FlutterFlowModel<AdminPageWidget> {
  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - Create Document] action in Button widget.
  ActivityRecord? inviteUsers9;
  // Stores action output result for [Backend Call - Create Document] action in Button widget.
  ActivityRecord? inviteUsers;
  // Stores action output result for [Backend Call - Create Document] action in Button widget.
  ActivityRecord? inviteUsers7;
  // Stores action output result for [Backend Call - Create Document] action in Button widget.
  ActivityRecord? inviteUsers3;
  // Stores action output result for [Backend Call - Create Document] action in Button widget.
  ActivityRecord? inviteUsers1;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
