import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import 'details_quiz_page_widget.dart' show DetailsQuizPageWidget;
import 'package:flutter/material.dart';

class DetailsQuizPageModel extends FlutterFlowModel<DetailsQuizPageWidget> {
  ///  Local state fields for this page.

  int? numberOfQuestions;

  QuizStruct? quiz;
  void updateQuizStruct(Function(QuizStruct) updateFn) {
    updateFn(quiz ??= QuizStruct());
  }

  int int1 = 0;

  String correct = ' ';

  int numCorrectAns = 0;

  ///  State fields for stateful widgets in this page.

  // State field(s) for CheckboxGroup widget.
  FormFieldController<List<String>>? checkboxGroupValueController;
  List<String>? get checkboxGroupValues => checkboxGroupValueController?.value;
  set checkboxGroupValues(List<String>? v) =>
      checkboxGroupValueController?.value = v;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
