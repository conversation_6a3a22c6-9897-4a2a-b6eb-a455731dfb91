import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

Future initFirebase() async {
  if (kIsWeb) {
    await Firebase.initializeApp(
        options: FirebaseOptions(
            apiKey: "AIzaSyBLbyLGrIXeD3Ti7fyazkye1qpG3JspAxU",
            authDomain: "wisebook-2c9da.firebaseapp.com",
            projectId: "wisebook-2c9da",
            storageBucket: "wisebook-2c9da.appspot.com",
            messagingSenderId: "427858747690",
            appId: "1:427858747690:web:a4caffa40e95748f11fe53",
            measurementId: "G-E7949NWD2L"));
  } else {
    await Firebase.initializeApp();
  }
}
