import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class CommentsRecord extends FirestoreRecord {
  CommentsRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "comment" field.
  String? _comment;
  String get comment => _comment ?? '';
  bool hasComment() => _comment != null;

  // "commentNumber" field.
  int? _commentNumber;
  int get commentNumber => _commentNumber ?? 0;
  bool hasCommentNumber() => _commentNumber != null;

  // "commentPost" field.
  DocumentReference? _commentPost;
  DocumentReference? get commentPost => _commentPost;
  bool hasCommentPost() => _commentPost != null;

  // "commentDate" field.
  DateTime? _commentDate;
  DateTime? get commentDate => _commentDate;
  bool hasCommentDate() => _commentDate != null;

  // "commentUser" field.
  DocumentReference? _commentUser;
  DocumentReference? get commentUser => _commentUser;
  bool hasCommentUser() => _commentUser != null;

  DocumentReference get parentReference => reference.parent.parent!;

  void _initializeFields() {
    _comment = snapshotData['comment'] as String?;
    _commentNumber = castToType<int>(snapshotData['commentNumber']);
    _commentPost = snapshotData['commentPost'] as DocumentReference?;
    _commentDate = snapshotData['commentDate'] as DateTime?;
    _commentUser = snapshotData['commentUser'] as DocumentReference?;
  }

  static Query<Map<String, dynamic>> collection([DocumentReference? parent]) =>
      parent != null
          ? parent.collection('comments')
          : FirebaseFirestore.instance.collectionGroup('comments');

  static DocumentReference createDoc(DocumentReference parent, {String? id}) =>
      parent.collection('comments').doc(id);

  static Stream<CommentsRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => CommentsRecord.fromSnapshot(s));

  static Future<CommentsRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => CommentsRecord.fromSnapshot(s));

  static CommentsRecord fromSnapshot(DocumentSnapshot snapshot) =>
      CommentsRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static CommentsRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      CommentsRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'CommentsRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is CommentsRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createCommentsRecordData({
  String? comment,
  int? commentNumber,
  DocumentReference? commentPost,
  DateTime? commentDate,
  DocumentReference? commentUser,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'comment': comment,
      'commentNumber': commentNumber,
      'commentPost': commentPost,
      'commentDate': commentDate,
      'commentUser': commentUser,
    }.withoutNulls,
  );

  return firestoreData;
}

class CommentsRecordDocumentEquality implements Equality<CommentsRecord> {
  const CommentsRecordDocumentEquality();

  @override
  bool equals(CommentsRecord? e1, CommentsRecord? e2) {
    return e1?.comment == e2?.comment &&
        e1?.commentNumber == e2?.commentNumber &&
        e1?.commentPost == e2?.commentPost &&
        e1?.commentDate == e2?.commentDate &&
        e1?.commentUser == e2?.commentUser;
  }

  @override
  int hash(CommentsRecord? e) => const ListEquality().hash([
        e?.comment,
        e?.commentNumber,
        e?.commentPost,
        e?.commentDate,
        e?.commentUser
      ]);

  @override
  bool isValidKey(Object? o) => o is CommentsRecord;
}
