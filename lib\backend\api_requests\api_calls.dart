import 'dart:convert';
import '../cloud_functions/cloud_functions.dart';

import 'package:flutter/foundation.dart';

import '/flutter_flow/flutter_flow_util.dart';
import 'api_manager.dart';

export 'api_manager.dart' show ApiCallResponse;

const _kPrivateApiFunctionName = 'ffPrivateApiCall';

class GetLivestreamIDCall {
  static Future<ApiCallResponse> call({
    String? playbackId = '',
  }) async {
    final response = await makeCloudCall(
      _kPrivateApiFunctionName,
      {
        'callName': 'GetLivestreamIDCall',
        'variables': {
          'playbackId': playbackId,
        },
      },
    );
    return ApiCallResponse.fromCloudCallResponse(response);
  }

  static String? livestreamID(dynamic response) =>
      castToType<String>(getJsonField(
        response,
        r'''$.data.object.id''',
      ));
}

class MyfatorahCall {
  static Future<ApiCallResponse> call({
    String? myfatoorahKey = '',
  }) async {
    return ApiManager.instance.makeApiCall(
      callName: 'myfatorah',
      apiUrl: 'https://api.myfatoorah.com/v2/${myfatoorahKey}',
      callType: ApiCallType.GET,
      headers: {},
      params: {},
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }
}

class GetPastLiveStreamCall {
  static Future<ApiCallResponse> call({
    String? streamId = '',
  }) async {
    final response = await makeCloudCall(
      _kPrivateApiFunctionName,
      {
        'callName': 'GetPastLiveStreamCall',
        'variables': {
          'streamId': streamId,
        },
      },
    );
    return ApiCallResponse.fromCloudCallResponse(response);
  }

  static String? playbackID(dynamic response) =>
      castToType<String>(getJsonField(
        response,
        r'''$.data[0].playback_ids[0].id''',
      ));
}

class ApiPagingParams {
  int nextPageNumber = 0;
  int numItems = 0;
  dynamic lastResponse;

  ApiPagingParams({
    required this.nextPageNumber,
    required this.numItems,
    required this.lastResponse,
  });

  @override
  String toString() =>
      'PagingParams(nextPageNumber: $nextPageNumber, numItems: $numItems, lastResponse: $lastResponse,)';
}

String _toEncodable(dynamic item) {
  if (item is DocumentReference) {
    return item.path;
  }
  return item;
}

String _serializeList(List? list) {
  list ??= <String>[];
  try {
    return json.encode(list, toEncodable: _toEncodable);
  } catch (_) {
    if (kDebugMode) {
      print("List serialization failed. Returning empty list.");
    }
    return '[]';
  }
}

String _serializeJson(dynamic jsonVar, [bool isList = false]) {
  jsonVar ??= (isList ? [] : {});
  try {
    return json.encode(jsonVar, toEncodable: _toEncodable);
  } catch (_) {
    if (kDebugMode) {
      print("Json serialization failed. Returning empty json.");
    }
    return isList ? '[]' : '{}';
  }
}
