{"indexes": [{"collectionGroup": "compettion", "queryScope": "COLLECTION", "fields": [{"fieldPath": "winner", "order": "ASCENDING"}, {"fieldPath": "host", "order": "ASCENDING"}, {"fieldPath": "testers", "arrayConfig": "CONTAINS"}, {"fieldPath": "time", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "users", "arrayConfig": "CONTAINS"}, {"fieldPath": "last_message_time", "order": "DESCENDING"}]}, {"collectionGroup": "chat_messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chat", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "activity", "queryScope": "COLLECTION", "fields": [{"fieldPath": "notifyUsers", "arrayConfig": "CONTAINS"}, {"fieldPath": "timePosted", "order": "DESCENDING"}]}, {"collectionGroup": "post", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "pageRef", "order": "ASCENDING"}, {"fieldPath": "time_posted", "order": "DESCENDING"}]}, {"collectionGroup": "post", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "groupRef", "order": "ASCENDING"}, {"fieldPath": "time_posted", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "post", "fieldPath": "post_user", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "story", "fieldPath": "userid", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "comments", "fieldPath": "commentUser", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "fcm_tokens", "fieldPath": "fcm_token", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "comments", "fieldPath": "commentPost", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "post", "fieldPath": "time_posted", "indexes": [{"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}]}