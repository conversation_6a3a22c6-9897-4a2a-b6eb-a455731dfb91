import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'edit_group_rules_widget.dart' show EditGroupRulesWidget;
import 'package:flutter/material.dart';

class EditGroupRulesModel extends FlutterFlowModel<EditGroupRulesWidget> {
  ///  Local state fields for this page.

  List<String> hastagsList = [];
  void addToHastagsList(String item) => hastagsList.add(item);
  void removeFromHastagsList(String item) => hastagsList.remove(item);
  void removeAtIndexFromHastagsList(int index) => hastagsList.removeAt(index);
  void insertAtIndexInHastagsList(int index, String item) =>
      hastagsList.insert(index, item);
  void updateHastagsListAtIndex(int index, Function(String) updateFn) =>
      hastagsList[index] = updateFn(hastagsList[index]);

  bool number = false;

  bool email = false;

  bool name = false;

  bool request = false;

  ///  State fields for stateful widgets in this page.

  final formKey = GlobalKey<FormState>();
  // State field(s) for description widget.
  FocusNode? descriptionFocusNode1;
  TextEditingController? descriptionTextController1;
  String? Function(BuildContext, String?)? descriptionTextController1Validator;
  // State field(s) for CheckboxName widget.
  bool? checkboxNameValue;
  // State field(s) for CheckboxMail widget.
  bool? checkboxMailValue;
  // State field(s) for CheckboxPH widget.
  bool? checkboxPHValue1;
  // State field(s) for CheckboxPH widget.
  bool? checkboxPHValue2;
  bool isDataUploading_uploadDatagroup23 = false;
  FFUploadedFile uploadedLocalFile_uploadDatagroup23 =
      FFUploadedFile(bytes: Uint8List.fromList([]));
  String uploadedFileUrl_uploadDatagroup23 = '';

  // State field(s) for productName widget.
  FocusNode? productNameFocusNode;
  TextEditingController? productNameTextController;
  String? Function(BuildContext, String?)? productNameTextControllerValidator;
  // State field(s) for description widget.
  FocusNode? descriptionFocusNode2;
  TextEditingController? descriptionTextController2;
  String? Function(BuildContext, String?)? descriptionTextController2Validator;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    descriptionFocusNode1?.dispose();
    descriptionTextController1?.dispose();

    productNameFocusNode?.dispose();
    productNameTextController?.dispose();

    descriptionFocusNode2?.dispose();
    descriptionTextController2?.dispose();
  }
}
