import 'dart:async';

import 'serialization_util.dart';
import '/backend/backend.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';


final _handledMessageIds = <String?>{};

class PushNotificationsHandler extends StatefulWidget {
  const PushNotificationsHandler({Key? key, required this.child})
      : super(key: key);

  final Widget child;

  @override
  _PushNotificationsHandlerState createState() =>
      _PushNotificationsHandlerState();
}

class _PushNotificationsHandlerState extends State<PushNotificationsHandler> {
  bool _loading = false;

  Future handleOpenedPushNotification() async {
    if (isWeb) {
      return;
    }

    final notification = await FirebaseMessaging.instance.getInitialMessage();
    if (notification != null) {
      await _handlePushNotification(notification);
    }
    FirebaseMessaging.onMessageOpenedApp.listen(_handlePushNotification);
  }

  Future _handlePushNotification(RemoteMessage message) async {
    if (_handledMessageIds.contains(message.messageId)) {
      return;
    }
    _handledMessageIds.add(message.messageId);

    safeSetState(() => _loading = true);
    try {
      final initialPageName = message.data['initialPageName'] as String;
      final initialParameterData = getInitialParameterData(message.data);
      final parametersBuilder = parametersBuilderMap[initialPageName];
      if (parametersBuilder != null) {
        final parameterData = await parametersBuilder(initialParameterData);
        if (mounted) {
          context.pushNamed(
            initialPageName,
            pathParameters: parameterData.pathParameters,
            extra: parameterData.extra,
          );
        } else {
          appNavigatorKey.currentContext?.pushNamed(
            initialPageName,
            pathParameters: parameterData.pathParameters,
            extra: parameterData.extra,
          );
        }
      }
    } catch (e) {
      print('Error: $e');
    } finally {
      safeSetState(() => _loading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      handleOpenedPushNotification();
    });
  }

  @override
  Widget build(BuildContext context) => _loading
      ? Container(
          color: Colors.transparent,
          child: Image.asset(
            'assets/images/Join_Us.png',
            fit: BoxFit.cover,
          ),
        )
      : widget.child;
}

class ParameterData {
  const ParameterData(
      {this.requiredParams = const {}, this.allParams = const {}});
  final Map<String, String?> requiredParams;
  final Map<String, dynamic> allParams;

  Map<String, String> get pathParameters => Map.fromEntries(
        requiredParams.entries
            .where((e) => e.value != null)
            .map((e) => MapEntry(e.key, e.value!)),
      );
  Map<String, dynamic> get extra => Map.fromEntries(
        allParams.entries.where((e) => e.value != null),
      );

  static Future<ParameterData> Function(Map<String, dynamic>) none() =>
      (data) async => ParameterData();
}

final parametersBuilderMap =
    <String, Future<ParameterData> Function(Map<String, dynamic>)>{
  'CreateSocialPost': (data) async => ParameterData(
        allParams: {
          'createrdisplayname':
              getParameter<String>(data, 'createrdisplayname'),
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
          'pageRef': getParameter<DocumentReference>(data, 'pageRef'),
        },
      ),
  'DetailsReviews': ParameterData.none(),
  'DetailsSocialPost': (data) async => ParameterData(
        allParams: {
          'postdocref': getParameter<DocumentReference>(data, 'postdocref'),
        },
      ),
  'DetailsArticleDetails': (data) async => ParameterData(
        allParams: {
          'postref': getParameter<String>(data, 'postref'),
        },
      ),
  'DetailsSupportForm': ParameterData.none(),
  'ListUserSearch': ParameterData.none(),
  'ListMessages': ParameterData.none(),
  'ProfileOtherUser': (data) async => ParameterData(
        allParams: {
          'userprofile': await getDocumentParameter<UsersRecord>(
              data, 'userprofile', UsersRecord.fromSnapshot),
        },
      ),
  'DetailsQuizPage': (data) async => ParameterData(
        allParams: {
          'competitonRef': await getDocumentParameter<CompettionRecord>(
              data, 'competitonRef', CompettionRecord.fromSnapshot),
        },
      ),
  'ListNotifications': ParameterData.none(),
  'SettingsPage': ParameterData.none(),
  'HomePage': ParameterData.none(),
  'profile': ParameterData.none(),
  'streams': ParameterData.none(),
  'livestreamer': (data) async => ParameterData(
        allParams: {
          'streamName': getParameter<String>(data, 'streamName'),
          'streamRef': getParameter<DocumentReference>(data, 'streamRef'),
        },
      ),
  'livestreamViewer': (data) async => ParameterData(
        allParams: {
          'url': getParameter<String>(data, 'url'),
        },
      ),
  'youtubeplayer': ParameterData.none(),
  'post': ParameterData.none(),
  'chat_2_Details': (data) async => ParameterData(
        allParams: {
          'chatRef': await getDocumentParameter<ChatsRecord>(
              data, 'chatRef', ChatsRecord.fromSnapshot),
        },
      ),
  'chat_2_main': ParameterData.none(),
  'chat_2_InviteUsers': (data) async => ParameterData(
        allParams: {
          'chatRef': await getDocumentParameter<ChatsRecord>(
              data, 'chatRef', ChatsRecord.fromSnapshot),
        },
      ),
  'image_Details': (data) async => ParameterData(
        allParams: {
          'chatMessage': await getDocumentParameter<ChatMessagesRecord>(
              data, 'chatMessage', ChatMessagesRecord.fromSnapshot),
        },
      ),
  'notifications_List': ParameterData.none(),
  'notification_Create': ParameterData.none(),
  'CreateComment': (data) async => ParameterData(
        allParams: {
          'postdocref': getParameter<DocumentReference>(data, 'postdocref'),
        },
      ),
  'auth_2_Create': ParameterData.none(),
  'auth_2_Login': ParameterData.none(),
  'auth_2_ForgotPassword': ParameterData.none(),
  'auth_2_createProfile': ParameterData.none(),
  'auth_2_Profile': ParameterData.none(),
  'auth_2_EditProfile': ParameterData.none(),
  'abc': ParameterData.none(),
  'audios': ParameterData.none(),
  'CreatePages': ParameterData.none(),
  'PageVIew': (data) async => ParameterData(
        allParams: {
          'pageRef': getParameter<DocumentReference>(data, 'pageRef'),
        },
      ),
  'AllPages': ParameterData.none(),
  'followers': (data) async => ParameterData(
        allParams: {
          'userRef': getParameter<DocumentReference>(data, 'userRef'),
        },
      ),
  'following': (data) async => ParameterData(
        allParams: {
          'userRef': getParameter<DocumentReference>(data, 'userRef'),
        },
      ),
  'myNotifications': ParameterData.none(),
  'CreateGroups': ParameterData.none(),
  'CreateGroupRules': (data) async => ParameterData(
        allParams: {
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
        },
      ),
  'AddgroupMembers': (data) async => ParameterData(
        allParams: {
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
        },
      ),
  'Groupview': (data) async => ParameterData(
        allParams: {
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
        },
      ),
  'auth_4_OnboardingOne': (data) async => ParameterData(
        allParams: {
          'index': getParameter<int>(data, 'index'),
        },
      ),
  'auth_4_Welcome': ParameterData.none(),
  'auth_4_OnboardingPhoneVerify': (data) async => ParameterData(
        allParams: {
          'phoneNumber': getParameter<String>(data, 'phoneNumber'),
          'isLogin': getParameter<bool>(data, 'isLogin'),
        },
      ),
  'auth_4_Login': ParameterData.none(),
  'GroupJoin': (data) async => ParameterData(
        allParams: {
          'index': getParameter<int>(data, 'index'),
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
        },
      ),
  'GroupRules': (data) async => ParameterData(
        allParams: {
          'index': getParameter<int>(data, 'index'),
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
        },
      ),
  'AdminPage': (data) async => ParameterData(
        allParams: {
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
          'userRef': getParameter<DocumentReference>(data, 'userRef'),
        },
      ),
  'activityCreate': ParameterData.none(),
  'AdminPanel': (data) async => ParameterData(
        allParams: {
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
          'whichPage': getParameter<String>(data, 'whichPage'),
        },
      ),
  'EditGroupRules': (data) async => ParameterData(
        allParams: {
          'groupRef': getParameter<DocumentReference>(data, 'groupRef'),
        },
      ),
  'search': ParameterData.none(),
  'Allgroups': ParameterData.none(),
  'CreateCompetition': ParameterData.none(),
  'AllCompetitons': ParameterData.none(),
  'MyCOmpetion': ParameterData.none(),
  'aboutus': ParameterData.none(),
  'TermsConditions': ParameterData.none(),
};

Map<String, dynamic> getInitialParameterData(Map<String, dynamic> data) {
  try {
    final parameterDataStr = data['parameterData'];
    if (parameterDataStr == null ||
        parameterDataStr is! String ||
        parameterDataStr.isEmpty) {
      return {};
    }
    return jsonDecode(parameterDataStr) as Map<String, dynamic>;
  } catch (e) {
    print('Error parsing parameter data: $e');
    return {};
  }
}
