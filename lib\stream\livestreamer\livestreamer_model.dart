import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'livestreamer_widget.dart' show LivestreamerWidget;
import 'package:flutter/material.dart';

class LivestreamerModel extends FlutterFlowModel<LivestreamerWidget> {
  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - Create Document] action in MuxBroadcast widget.
  StreamsRecord? createdDoc;
  // Stores action output result for [Backend Call - Create Document] action in Button widget.
  ActivityRecord? inviteUsers;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
