import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class PostRecord extends FirestoreRecord {
  PostRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "post_photo" field.
  String? _postPhoto;
  String get postPhoto => _postPhoto ?? '';
  bool hasPostPhoto() => _postPhoto != null;

  // "post_description" field.
  String? _postDescription;
  String get postDescription => _postDescription ?? '';
  bool hasPostDescription() => _postDescription != null;

  // "time_posted" field.
  DateTime? _timePosted;
  DateTime? get timePosted => _timePosted;
  bool hasTimePosted() => _timePosted != null;

  // "num_comments" field.
  int? _numComments;
  int get numComments => _numComments ?? 0;
  bool hasNumComments() => _numComments != null;

  // "num_votes" field.
  int? _numVotes;
  int get numVotes => _numVotes ?? 0;
  bool hasNumVotes() => _numVotes != null;

  // "pollQuestion" field.
  String? _pollQuestion;
  String get pollQuestion => _pollQuestion ?? '';
  bool hasPollQuestion() => _pollQuestion != null;

  // "pollOption1" field.
  String? _pollOption1;
  String get pollOption1 => _pollOption1 ?? '';
  bool hasPollOption1() => _pollOption1 != null;

  // "pollOption2" field.
  String? _pollOption2;
  String get pollOption2 => _pollOption2 ?? '';
  bool hasPollOption2() => _pollOption2 != null;

  // "savedPost" field.
  int? _savedPost;
  int get savedPost => _savedPost ?? 0;
  bool hasSavedPost() => _savedPost != null;

  // "post_title" field.
  String? _postTitle;
  String get postTitle => _postTitle ?? '';
  bool hasPostTitle() => _postTitle != null;

  // "commentUsers" field.
  List<DocumentReference>? _commentUsers;
  List<DocumentReference> get commentUsers => _commentUsers ?? const [];
  bool hasCommentUsers() => _commentUsers != null;

  // "savedUsers" field.
  List<DocumentReference>? _savedUsers;
  List<DocumentReference> get savedUsers => _savedUsers ?? const [];
  bool hasSavedUsers() => _savedUsers != null;

  // "post_user" field.
  DocumentReference? _postUser;
  DocumentReference? get postUser => _postUser;
  bool hasPostUser() => _postUser != null;

  // "likes" field.
  List<DocumentReference>? _likes;
  List<DocumentReference> get likes => _likes ?? const [];
  bool hasLikes() => _likes != null;

  // "pollOpt1" field.
  List<DocumentReference>? _pollOpt1;
  List<DocumentReference> get pollOpt1 => _pollOpt1 ?? const [];
  bool hasPollOpt1() => _pollOpt1 != null;

  // "pollOpt2" field.
  List<DocumentReference>? _pollOpt2;
  List<DocumentReference> get pollOpt2 => _pollOpt2 ?? const [];
  bool hasPollOpt2() => _pollOpt2 != null;

  // "votedUser" field.
  List<DocumentReference>? _votedUser;
  List<DocumentReference> get votedUser => _votedUser ?? const [];
  bool hasVotedUser() => _votedUser != null;

  // "pageRef" field.
  DocumentReference? _pageRef;
  DocumentReference? get pageRef => _pageRef;
  bool hasPageRef() => _pageRef != null;

  // "groupRef" field.
  DocumentReference? _groupRef;
  DocumentReference? get groupRef => _groupRef;
  bool hasGroupRef() => _groupRef != null;

  DocumentReference get parentReference => reference.parent.parent!;

  void _initializeFields() {
    _postPhoto = snapshotData['post_photo'] as String?;
    _postDescription = snapshotData['post_description'] as String?;
    _timePosted = snapshotData['time_posted'] as DateTime?;
    _numComments = castToType<int>(snapshotData['num_comments']);
    _numVotes = castToType<int>(snapshotData['num_votes']);
    _pollQuestion = snapshotData['pollQuestion'] as String?;
    _pollOption1 = snapshotData['pollOption1'] as String?;
    _pollOption2 = snapshotData['pollOption2'] as String?;
    _savedPost = castToType<int>(snapshotData['savedPost']);
    _postTitle = snapshotData['post_title'] as String?;
    _commentUsers = getDataList(snapshotData['commentUsers']);
    _savedUsers = getDataList(snapshotData['savedUsers']);
    _postUser = snapshotData['post_user'] as DocumentReference?;
    _likes = getDataList(snapshotData['likes']);
    _pollOpt1 = getDataList(snapshotData['pollOpt1']);
    _pollOpt2 = getDataList(snapshotData['pollOpt2']);
    _votedUser = getDataList(snapshotData['votedUser']);
    _pageRef = snapshotData['pageRef'] as DocumentReference?;
    _groupRef = snapshotData['groupRef'] as DocumentReference?;
  }

  static Query<Map<String, dynamic>> collection([DocumentReference? parent]) =>
      parent != null
          ? parent.collection('post')
          : FirebaseFirestore.instance.collectionGroup('post');

  static DocumentReference createDoc(DocumentReference parent, {String? id}) =>
      parent.collection('post').doc(id);

  static Stream<PostRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => PostRecord.fromSnapshot(s));

  static Future<PostRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => PostRecord.fromSnapshot(s));

  static PostRecord fromSnapshot(DocumentSnapshot snapshot) => PostRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static PostRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      PostRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'PostRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is PostRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createPostRecordData({
  String? postPhoto,
  String? postDescription,
  DateTime? timePosted,
  int? numComments,
  int? numVotes,
  String? pollQuestion,
  String? pollOption1,
  String? pollOption2,
  int? savedPost,
  String? postTitle,
  DocumentReference? postUser,
  DocumentReference? pageRef,
  DocumentReference? groupRef,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'post_photo': postPhoto,
      'post_description': postDescription,
      'time_posted': timePosted,
      'num_comments': numComments,
      'num_votes': numVotes,
      'pollQuestion': pollQuestion,
      'pollOption1': pollOption1,
      'pollOption2': pollOption2,
      'savedPost': savedPost,
      'post_title': postTitle,
      'post_user': postUser,
      'pageRef': pageRef,
      'groupRef': groupRef,
    }.withoutNulls,
  );

  return firestoreData;
}

class PostRecordDocumentEquality implements Equality<PostRecord> {
  const PostRecordDocumentEquality();

  @override
  bool equals(PostRecord? e1, PostRecord? e2) {
    const listEquality = ListEquality();
    return e1?.postPhoto == e2?.postPhoto &&
        e1?.postDescription == e2?.postDescription &&
        e1?.timePosted == e2?.timePosted &&
        e1?.numComments == e2?.numComments &&
        e1?.numVotes == e2?.numVotes &&
        e1?.pollQuestion == e2?.pollQuestion &&
        e1?.pollOption1 == e2?.pollOption1 &&
        e1?.pollOption2 == e2?.pollOption2 &&
        e1?.savedPost == e2?.savedPost &&
        e1?.postTitle == e2?.postTitle &&
        listEquality.equals(e1?.commentUsers, e2?.commentUsers) &&
        listEquality.equals(e1?.savedUsers, e2?.savedUsers) &&
        e1?.postUser == e2?.postUser &&
        listEquality.equals(e1?.likes, e2?.likes) &&
        listEquality.equals(e1?.pollOpt1, e2?.pollOpt1) &&
        listEquality.equals(e1?.pollOpt2, e2?.pollOpt2) &&
        listEquality.equals(e1?.votedUser, e2?.votedUser) &&
        e1?.pageRef == e2?.pageRef &&
        e1?.groupRef == e2?.groupRef;
  }

  @override
  int hash(PostRecord? e) => const ListEquality().hash([
        e?.postPhoto,
        e?.postDescription,
        e?.timePosted,
        e?.numComments,
        e?.numVotes,
        e?.pollQuestion,
        e?.pollOption1,
        e?.pollOption2,
        e?.savedPost,
        e?.postTitle,
        e?.commentUsers,
        e?.savedUsers,
        e?.postUser,
        e?.likes,
        e?.pollOpt1,
        e?.pollOpt2,
        e?.votedUser,
        e?.pageRef,
        e?.groupRef
      ]);

  @override
  bool isValidKey(Object? o) => o is PostRecord;
}
