const axios = require("axios").default;
const qs = require("qs");

async function _getLivestreamIDCall(context, ffVariables) {
  var playbackId = ffVariables["playbackId"];

  var url = `https://api.mux.com/video/v1/playback-ids/${playbackId}`;
  var headers = {
    Authorization: `Basic YTY5OWY5ZDQtZDcwZS00MjE4LThlMGYtMGY1NWY5YTEzYzlhOnYrSm81K2VWcDhzWitnYWlkenNoQ3p5VlYza3lSZlZudzRQYm9tNU8wRGFUQWpzQTZUa2taWVFhRUhFQnUvcG90eURTcXpxNkpIbg==`,
  };
  var params = {};
  var ffApiRequestBody = undefined;

  return makeApiRequest({
    method: "get",
    url,
    headers,
    params,
    returnBody: true,
    isStreamingApi: false,
  });
}
async function _getPastLiveStreamCall(context, ffVariables) {
  var streamId = ffVariables["streamId"];

  var url = `https://api.mux.com/video/v1/assets`;
  var headers = {
    Authorization: `Basic YTY5OWY5ZDQtZDcwZS00MjE4LThlMGYtMGY1NWY5YTEzYzlhOnYrSm81K2VWcDhzWitnYWlkenNoQ3p5VlYza3lSZlZudzRQYm9tNU8wRGFUQWpzQTZUa2taWVFhRUhFQnUvcG90eURTcXpxNkpIbg==`,
  };
  var params = { live_stream_id: streamId };
  var ffApiRequestBody = undefined;

  return makeApiRequest({
    method: "get",
    url,
    headers,
    params,
    returnBody: true,
    isStreamingApi: false,
  });
}

/// Helper functions to route to the appropriate API Call.

async function makeApiCall(context, data) {
  var callName = data["callName"] || "";
  var variables = data["variables"] || {};

  const callMap = {
    GetLivestreamIDCall: _getLivestreamIDCall,
    GetPastLiveStreamCall: _getPastLiveStreamCall,
  };

  if (!(callName in callMap)) {
    return {
      statusCode: 400,
      error: `API Call "${callName}" not defined as private API.`,
    };
  }

  var apiCall = callMap[callName];
  var response = await apiCall(context, variables);
  return response;
}

async function makeApiRequest({
  method,
  url,
  headers,
  params,
  body,
  returnBody,
  isStreamingApi,
}) {
  return axios
    .request({
      method: method,
      url: url,
      headers: headers,
      params: params,
      responseType: isStreamingApi ? "stream" : "json",
      ...(body && { data: body }),
    })
    .then((response) => {
      return {
        statusCode: response.status,
        headers: response.headers,
        ...(returnBody && { body: response.data }),
        isStreamingApi: isStreamingApi,
      };
    })
    .catch(function (error) {
      return {
        statusCode: error.response.status,
        headers: error.response.headers,
        ...(returnBody && { body: error.response.data }),
        error: error.message,
      };
    });
}

const _unauthenticatedResponse = {
  statusCode: 401,
  headers: {},
  error: "API call requires authentication",
};

function createBody({ headers, params, body, bodyType }) {
  switch (bodyType) {
    case "JSON":
      headers["Content-Type"] = "application/json";
      return body;
    case "TEXT":
      headers["Content-Type"] = "text/plain";
      return body;
    case "X_WWW_FORM_URL_ENCODED":
      headers["Content-Type"] = "application/x-www-form-urlencoded";
      return qs.stringify(params);
  }
}

module.exports = { makeApiCall };
