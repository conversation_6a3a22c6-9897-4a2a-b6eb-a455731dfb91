import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class NotificationRecord extends FirestoreRecord {
  NotificationRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "postid" field.
  DocumentReference? _postid;
  DocumentReference? get postid => _postid;
  bool hasPostid() => _postid != null;

  // "notTime" field.
  DateTime? _notTime;
  DateTime? get notTime => _notTime;
  bool hasNotTime() => _notTime != null;

  // "notID" field.
  DocumentReference? _notID;
  DocumentReference? get notID => _notID;
  bool hasNotID() => _notID != null;

  // "userid" field.
  DocumentReference? _userid;
  DocumentReference? get userid => _userid;
  bool hasUserid() => _userid != null;

  // "likedUsers" field.
  List<DocumentReference>? _likedUsers;
  List<DocumentReference> get likedUsers => _likedUsers ?? const [];
  bool hasLikedUsers() => _likedUsers != null;

  DocumentReference get parentReference => reference.parent.parent!;

  void _initializeFields() {
    _postid = snapshotData['postid'] as DocumentReference?;
    _notTime = snapshotData['notTime'] as DateTime?;
    _notID = snapshotData['notID'] as DocumentReference?;
    _userid = snapshotData['userid'] as DocumentReference?;
    _likedUsers = getDataList(snapshotData['likedUsers']);
  }

  static Query<Map<String, dynamic>> collection([DocumentReference? parent]) =>
      parent != null
          ? parent.collection('notification')
          : FirebaseFirestore.instance.collectionGroup('notification');

  static DocumentReference createDoc(DocumentReference parent, {String? id}) =>
      parent.collection('notification').doc(id);

  static Stream<NotificationRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => NotificationRecord.fromSnapshot(s));

  static Future<NotificationRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => NotificationRecord.fromSnapshot(s));

  static NotificationRecord fromSnapshot(DocumentSnapshot snapshot) =>
      NotificationRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static NotificationRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      NotificationRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'NotificationRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is NotificationRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createNotificationRecordData({
  DocumentReference? postid,
  DateTime? notTime,
  DocumentReference? notID,
  DocumentReference? userid,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'postid': postid,
      'notTime': notTime,
      'notID': notID,
      'userid': userid,
    }.withoutNulls,
  );

  return firestoreData;
}

class NotificationRecordDocumentEquality
    implements Equality<NotificationRecord> {
  const NotificationRecordDocumentEquality();

  @override
  bool equals(NotificationRecord? e1, NotificationRecord? e2) {
    const listEquality = ListEquality();
    return e1?.postid == e2?.postid &&
        e1?.notTime == e2?.notTime &&
        e1?.notID == e2?.notID &&
        e1?.userid == e2?.userid &&
        listEquality.equals(e1?.likedUsers, e2?.likedUsers);
  }

  @override
  int hash(NotificationRecord? e) => const ListEquality()
      .hash([e?.postid, e?.notTime, e?.notID, e?.userid, e?.likedUsers]);

  @override
  bool isValidKey(Object? o) => o is NotificationRecord;
}
