import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class UserRecord extends FirestoreRecord {
  UserRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "email" field.
  String? _email;
  String get email => _email ?? '';
  bool hasEmail() => _email != null;

  // "display_name" field.
  String? _displayName;
  String get displayName => _displayName ?? '';
  bool hasDisplayName() => _displayName != null;

  // "photo_url" field.
  String? _photoUrl;
  String get photoUrl => _photoUrl ?? '';
  bool hasPhotoUrl() => _photoUrl != null;

  // "uid" field.
  String? _uid;
  String get uid => _uid ?? '';
  bool hasUid() => _uid != null;

  // "phone_number" field.
  String? _phoneNumber;
  String get phoneNumber => _phoneNumber ?? '';
  bool hasPhoneNumber() => _phoneNumber != null;

  // "username" field.
  String? _username;
  String get username => _username ?? '';
  bool hasUsername() => _username != null;

  // "fullname" field.
  String? _fullname;
  String get fullname => _fullname ?? '';
  bool hasFullname() => _fullname != null;

  // "created_time" field.
  DateTime? _createdTime;
  DateTime? get createdTime => _createdTime;
  bool hasCreatedTime() => _createdTime != null;

  // "location" field.
  String? _location;
  String get location => _location ?? '';
  bool hasLocation() => _location != null;

  // "website" field.
  String? _website;
  String get website => _website ?? '';
  bool hasWebsite() => _website != null;

  // "job" field.
  String? _job;
  String get job => _job ?? '';
  bool hasJob() => _job != null;

  // "bio" field.
  String? _bio;
  String get bio => _bio ?? '';
  bool hasBio() => _bio != null;

  // "coverPic" field.
  String? _coverPic;
  String get coverPic => _coverPic ?? '';
  bool hasCoverPic() => _coverPic != null;

  // "joined" field.
  DateTime? _joined;
  DateTime? get joined => _joined;
  bool hasJoined() => _joined != null;

  // "following" field.
  List<DocumentReference>? _following;
  List<DocumentReference> get following => _following ?? const [];
  bool hasFollowing() => _following != null;

  // "follow" field.
  List<DocumentReference>? _follow;
  List<DocumentReference> get follow => _follow ?? const [];
  bool hasFollow() => _follow != null;

  // "birthday" field.
  String? _birthday;
  String get birthday => _birthday ?? '';
  bool hasBirthday() => _birthday != null;

  // "role" field.
  String? _role;
  String get role => _role ?? '';
  bool hasRole() => _role != null;

  void _initializeFields() {
    _email = snapshotData['email'] as String?;
    _displayName = snapshotData['display_name'] as String?;
    _photoUrl = snapshotData['photo_url'] as String?;
    _uid = snapshotData['uid'] as String?;
    _phoneNumber = snapshotData['phone_number'] as String?;
    _username = snapshotData['username'] as String?;
    _fullname = snapshotData['fullname'] as String?;
    _createdTime = snapshotData['created_time'] as DateTime?;
    _location = snapshotData['location'] as String?;
    _website = snapshotData['website'] as String?;
    _job = snapshotData['job'] as String?;
    _bio = snapshotData['bio'] as String?;
    _coverPic = snapshotData['coverPic'] as String?;
    _joined = snapshotData['joined'] as DateTime?;
    _following = getDataList(snapshotData['following']);
    _follow = getDataList(snapshotData['follow']);
    _birthday = snapshotData['birthday'] as String?;
    _role = snapshotData['role'] as String?;
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('user');

  static Stream<UserRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => UserRecord.fromSnapshot(s));

  static Future<UserRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => UserRecord.fromSnapshot(s));

  static UserRecord fromSnapshot(DocumentSnapshot snapshot) => UserRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static UserRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      UserRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'UserRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is UserRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createUserRecordData({
  String? email,
  String? displayName,
  String? photoUrl,
  String? uid,
  String? phoneNumber,
  String? username,
  String? fullname,
  DateTime? createdTime,
  String? location,
  String? website,
  String? job,
  String? bio,
  String? coverPic,
  DateTime? joined,
  String? birthday,
  String? role,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'email': email,
      'display_name': displayName,
      'photo_url': photoUrl,
      'uid': uid,
      'phone_number': phoneNumber,
      'username': username,
      'fullname': fullname,
      'created_time': createdTime,
      'location': location,
      'website': website,
      'job': job,
      'bio': bio,
      'coverPic': coverPic,
      'joined': joined,
      'birthday': birthday,
      'role': role,
    }.withoutNulls,
  );

  return firestoreData;
}

class UserRecordDocumentEquality implements Equality<UserRecord> {
  const UserRecordDocumentEquality();

  @override
  bool equals(UserRecord? e1, UserRecord? e2) {
    const listEquality = ListEquality();
    return e1?.email == e2?.email &&
        e1?.displayName == e2?.displayName &&
        e1?.photoUrl == e2?.photoUrl &&
        e1?.uid == e2?.uid &&
        e1?.phoneNumber == e2?.phoneNumber &&
        e1?.username == e2?.username &&
        e1?.fullname == e2?.fullname &&
        e1?.createdTime == e2?.createdTime &&
        e1?.location == e2?.location &&
        e1?.website == e2?.website &&
        e1?.job == e2?.job &&
        e1?.bio == e2?.bio &&
        e1?.coverPic == e2?.coverPic &&
        e1?.joined == e2?.joined &&
        listEquality.equals(e1?.following, e2?.following) &&
        listEquality.equals(e1?.follow, e2?.follow) &&
        e1?.birthday == e2?.birthday &&
        e1?.role == e2?.role;
  }

  @override
  int hash(UserRecord? e) => const ListEquality().hash([
        e?.email,
        e?.displayName,
        e?.photoUrl,
        e?.uid,
        e?.phoneNumber,
        e?.username,
        e?.fullname,
        e?.createdTime,
        e?.location,
        e?.website,
        e?.job,
        e?.bio,
        e?.coverPic,
        e?.joined,
        e?.following,
        e?.follow,
        e?.birthday,
        e?.role
      ]);

  @override
  bool isValidKey(Object? o) => o is UserRecord;
}
