import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class CompettionRecord extends FirestoreRecord {
  CompettionRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "competitonName" field.
  String? _competitonName;
  String get competitonName => _competitonName ?? '';
  bool hasCompetitonName() => _competitonName != null;

  // "quizQuestions" field.
  List<QuizStruct>? _quizQuestions;
  List<QuizStruct> get quizQuestions => _quizQuestions ?? const [];
  bool hasQuizQuestions() => _quizQuestions != null;

  // "winner" field.
  DocumentReference? _winner;
  DocumentReference? get winner => _winner;
  bool hasWinner() => _winner != null;

  // "host" field.
  DocumentReference? _host;
  DocumentReference? get host => _host;
  bool hasHost() => _host != null;

  // "time" field.
  DateTime? _time;
  DateTime? get time => _time;
  bool hasTime() => _time != null;

  // "participants" field.
  ScoringStruct? _participants;
  ScoringStruct get participants => _participants ?? ScoringStruct();
  bool hasParticipants() => _participants != null;

  // "hostName" field.
  String? _hostName;
  String get hostName => _hostName ?? '';
  bool hasHostName() => _hostName != null;

  // "winnerBool" field.
  bool? _winnerBool;
  bool get winnerBool => _winnerBool ?? false;
  bool hasWinnerBool() => _winnerBool != null;

  // "hostBool" field.
  bool? _hostBool;
  bool get hostBool => _hostBool ?? false;
  bool hasHostBool() => _hostBool != null;

  // "testers" field.
  List<DocumentReference>? _testers;
  List<DocumentReference> get testers => _testers ?? const [];
  bool hasTesters() => _testers != null;

  void _initializeFields() {
    _competitonName = snapshotData['competitonName'] as String?;
    _quizQuestions = getStructList(
      snapshotData['quizQuestions'],
      QuizStruct.fromMap,
    );
    _winner = snapshotData['winner'] as DocumentReference?;
    _host = snapshotData['host'] as DocumentReference?;
    _time = snapshotData['time'] as DateTime?;
    _participants = snapshotData['participants'] is ScoringStruct
        ? snapshotData['participants']
        : ScoringStruct.maybeFromMap(snapshotData['participants']);
    _hostName = snapshotData['hostName'] as String?;
    _winnerBool = snapshotData['winnerBool'] as bool?;
    _hostBool = snapshotData['hostBool'] as bool?;
    _testers = getDataList(snapshotData['testers']);
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('compettion');

  static Stream<CompettionRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => CompettionRecord.fromSnapshot(s));

  static Future<CompettionRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => CompettionRecord.fromSnapshot(s));

  static CompettionRecord fromSnapshot(DocumentSnapshot snapshot) =>
      CompettionRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static CompettionRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      CompettionRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'CompettionRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is CompettionRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createCompettionRecordData({
  String? competitonName,
  DocumentReference? winner,
  DocumentReference? host,
  DateTime? time,
  ScoringStruct? participants,
  String? hostName,
  bool? winnerBool,
  bool? hostBool,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'competitonName': competitonName,
      'winner': winner,
      'host': host,
      'time': time,
      'participants': ScoringStruct().toMap(),
      'hostName': hostName,
      'winnerBool': winnerBool,
      'hostBool': hostBool,
    }.withoutNulls,
  );

  // Handle nested data for "participants" field.
  addScoringStructData(firestoreData, participants, 'participants');

  return firestoreData;
}

class CompettionRecordDocumentEquality implements Equality<CompettionRecord> {
  const CompettionRecordDocumentEquality();

  @override
  bool equals(CompettionRecord? e1, CompettionRecord? e2) {
    const listEquality = ListEquality();
    return e1?.competitonName == e2?.competitonName &&
        listEquality.equals(e1?.quizQuestions, e2?.quizQuestions) &&
        e1?.winner == e2?.winner &&
        e1?.host == e2?.host &&
        e1?.time == e2?.time &&
        e1?.participants == e2?.participants &&
        e1?.hostName == e2?.hostName &&
        e1?.winnerBool == e2?.winnerBool &&
        e1?.hostBool == e2?.hostBool &&
        listEquality.equals(e1?.testers, e2?.testers);
  }

  @override
  int hash(CompettionRecord? e) => const ListEquality().hash([
        e?.competitonName,
        e?.quizQuestions,
        e?.winner,
        e?.host,
        e?.time,
        e?.participants,
        e?.hostName,
        e?.winnerBool,
        e?.hostBool,
        e?.testers
      ]);

  @override
  bool isValidKey(Object? o) => o is CompettionRecord;
}
