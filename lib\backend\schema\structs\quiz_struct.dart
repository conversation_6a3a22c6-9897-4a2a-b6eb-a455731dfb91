// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';
import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class QuizStruct extends FFFirebaseStruct {
  QuizStruct({
    String? question,
    List<String>? answers,
    String? correctOne,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _question = question,
        _answers = answers,
        _correctOne = correctOne,
        super(firestoreUtilData);

  // "question" field.
  String? _question;
  String get question => _question ?? '';
  set question(String? val) => _question = val;

  bool hasQuestion() => _question != null;

  // "answers" field.
  List<String>? _answers;
  List<String> get answers => _answers ?? const [];
  set answers(List<String>? val) => _answers = val;

  void updateAnswers(Function(List<String>) updateFn) {
    updateFn(_answers ??= []);
  }

  bool hasAnswers() => _answers != null;

  // "correctOne" field.
  String? _correctOne;
  String get correctOne => _correctOne ?? '';
  set correctOne(String? val) => _correctOne = val;

  bool hasCorrectOne() => _correctOne != null;

  static QuizStruct fromMap(Map<String, dynamic> data) => QuizStruct(
        question: data['question'] as String?,
        answers: getDataList(data['answers']),
        correctOne: data['correctOne'] as String?,
      );

  static QuizStruct? maybeFromMap(dynamic data) =>
      data is Map ? QuizStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'question': _question,
        'answers': _answers,
        'correctOne': _correctOne,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'question': serializeParam(
          _question,
          ParamType.String,
        ),
        'answers': serializeParam(
          _answers,
          ParamType.String,
          isList: true,
        ),
        'correctOne': serializeParam(
          _correctOne,
          ParamType.String,
        ),
      }.withoutNulls;

  static QuizStruct fromSerializableMap(Map<String, dynamic> data) =>
      QuizStruct(
        question: deserializeParam(
          data['question'],
          ParamType.String,
          false,
        ),
        answers: deserializeParam<String>(
          data['answers'],
          ParamType.String,
          true,
        ),
        correctOne: deserializeParam(
          data['correctOne'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'QuizStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is QuizStruct &&
        question == other.question &&
        listEquality.equals(answers, other.answers) &&
        correctOne == other.correctOne;
  }

  @override
  int get hashCode =>
      const ListEquality().hash([question, answers, correctOne]);
}

QuizStruct createQuizStruct({
  String? question,
  String? correctOne,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    QuizStruct(
      question: question,
      correctOne: correctOne,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

QuizStruct? updateQuizStruct(
  QuizStruct? quiz, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    quiz
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addQuizStructData(
  Map<String, dynamic> firestoreData,
  QuizStruct? quiz,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (quiz == null) {
    return;
  }
  if (quiz.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields = !forFieldValue && quiz.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final quizData = getQuizFirestoreData(quiz, forFieldValue);
  final nestedData = quizData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = quiz.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getQuizFirestoreData(
  QuizStruct? quiz, [
  bool forFieldValue = false,
]) {
  if (quiz == null) {
    return {};
  }
  final firestoreData = mapToFirestore(quiz.toMap());

  // Add any Firestore field values
  quiz.firestoreUtilData.fieldValues.forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getQuizListFirestoreData(
  List<QuizStruct>? quizs,
) =>
    quizs?.map((e) => getQuizFirestoreData(e, true)).toList() ?? [];
