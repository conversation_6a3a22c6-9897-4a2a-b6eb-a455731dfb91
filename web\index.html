<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    Alternatively, build your application passing the --base-href parameter
    specifying the new root path of your web app.

    Fore more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="WiseBook">

  

  

  <!-- Favicon -->
  <link rel="icon" href="logoWisebook32_32.png" sizes="any"/>
  

  <!-- Open Graph & SEO tags -->
  <meta property="og:title" content="wisebook" />
  <meta property="og:description" content="Wisebase is a social networking application that allows users to sign up for free profiles and connect with friends, colleagues, or new acquaintances online" />
  <meta property="og:image" content="/assets/assets/images/wisebooklogo.png" /> 
  <meta name="twitter:title" content="wisebook" />
  <meta name="twitter:description" content="Wisebase is a social networking application that allows users to sign up for free profiles and connect with friends, colleagues, or new acquaintances online" />
  <meta name="twitter:image" content="/assets/assets/images/wisebooklogo.png" />
  <meta name="twitter:card" content="summary_large_image" />

  <title> wisebook </title>
  <meta name="description" content="Wisebase is a social networking application that allows users to sign up for free profiles and connect with friends, colleagues, or new acquaintances online" />
    

  <!-- Status Bar color in Safari browser (iOS) and PWA -->
  <meta name="theme-color" media="(prefers-color-scheme: light)" content="#f1f4f8">
  <meta name="theme-color" media="(prefers-color-scheme: dark)"  content="#1d2428">

  <link rel="manifest" href="manifest.json">
  
</head>
<body>
  
  
  
  
  
  <script>
    {{flutter_bootstrap_js}}
  </script>
</body>
</html>
