import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import '/index.dart';
import 'create_competition_widget.dart' show CreateCompetitionWidget;
import 'package:flutter/material.dart';

class CreateCompetitionModel extends FlutterFlowModel<CreateCompetitionWidget> {
  ///  Local state fields for this page.

  String? ans;

  List<String> wrongAnswer = [];
  void addToWrongAnswer(String item) => wrongAnswer.add(item);
  void removeFromWrongAnswer(String item) => wrongAnswer.remove(item);
  void removeAtIndexFromWrongAnswer(int index) => wrongAnswer.removeAt(index);
  void insertAtIndexInWrongAnswer(int index, String item) =>
      wrongAnswer.insert(index, item);
  void updateWrongAnswerAtIndex(int index, Function(String) updateFn) =>
      wrongAnswer[index] = updateFn(wrongAnswer[index]);

  List<QuizStruct> quizQuestuions = [];
  void addToQuizQuestuions(QuizStruct item) => quizQuestuions.add(item);
  void removeFromQuizQuestuions(QuizStruct item) => quizQuestuions.remove(item);
  void removeAtIndexFromQuizQuestuions(int index) =>
      quizQuestuions.removeAt(index);
  void insertAtIndexInQuizQuestuions(int index, QuizStruct item) =>
      quizQuestuions.insert(index, item);
  void updateQuizQuestuionsAtIndex(int index, Function(QuizStruct) updateFn) =>
      quizQuestuions[index] = updateFn(quizQuestuions[index]);

  ///  State fields for stateful widgets in this page.

  final formKey2 = GlobalKey<FormState>();
  final formKey1 = GlobalKey<FormState>();
  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController1;
  String? Function(BuildContext, String?)? textController1Validator;
  String? _textController1Validator(BuildContext context, String? val) {
    if (val == null || val.isEmpty) {
      return FFLocalizations.of(context).getText(
        'n5mupjhi' /* Field is required */,
      );
    }

    return null;
  }

  // State field(s) for question widget.
  FocusNode? questionFocusNode;
  TextEditingController? questionTextController;
  String? Function(BuildContext, String?)? questionTextControllerValidator;
  String? _questionTextControllerValidator(BuildContext context, String? val) {
    if (val == null || val.isEmpty) {
      return FFLocalizations.of(context).getText(
        '633wd0al' /* Field is required */,
      );
    }

    return null;
  }

  // State field(s) for incorrect widget.
  FocusNode? incorrectFocusNode;
  TextEditingController? incorrectTextController;
  String? Function(BuildContext, String?)? incorrectTextControllerValidator;
  String? _incorrectTextControllerValidator(BuildContext context, String? val) {
    if (val == null || val.isEmpty) {
      return FFLocalizations.of(context).getText(
        'kpj57yjo' /* Field is required */,
      );
    }

    return null;
  }

  // State field(s) for ChoiceChips widget.
  FormFieldController<List<String>>? choiceChipsValueController;
  String? get choiceChipsValue =>
      choiceChipsValueController?.value?.firstOrNull;
  set choiceChipsValue(String? val) =>
      choiceChipsValueController?.value = val != null ? [val] : [];
  // State field(s) for Correct widget.
  FocusNode? correctFocusNode;
  TextEditingController? correctTextController;
  String? Function(BuildContext, String?)? correctTextControllerValidator;
  String? _correctTextControllerValidator(BuildContext context, String? val) {
    if (val == null || val.isEmpty) {
      return FFLocalizations.of(context).getText(
        'wz5hbwrp' /* Field is required */,
      );
    }

    return null;
  }

  @override
  void initState(BuildContext context) {
    textController1Validator = _textController1Validator;
    questionTextControllerValidator = _questionTextControllerValidator;
    incorrectTextControllerValidator = _incorrectTextControllerValidator;
    correctTextControllerValidator = _correctTextControllerValidator;
  }

  @override
  void dispose() {
    textFieldFocusNode?.dispose();
    textController1?.dispose();

    questionFocusNode?.dispose();
    questionTextController?.dispose();

    incorrectFocusNode?.dispose();
    incorrectTextController?.dispose();

    correctFocusNode?.dispose();
    correctTextController?.dispose();
  }
}
