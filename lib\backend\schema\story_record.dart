import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class StoryRecord extends FirestoreRecord {
  StoryRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "username" field.
  String? _username;
  String get username => _username ?? '';
  bool hasUsername() => _username != null;

  // "createdTime" field.
  DateTime? _createdTime;
  DateTime? get createdTime => _createdTime;
  bool hasCreatedTime() => _createdTime != null;

  // "storyImage" field.
  String? _storyImage;
  String get storyImage => _storyImage ?? '';
  bool hasStoryImage() => _storyImage != null;

  // "storyid" field.
  DocumentReference? _storyid;
  DocumentReference? get storyid => _storyid;
  bool hasStoryid() => _storyid != null;

  // "clicked" field.
  bool? _clicked;
  bool get clicked => _clicked ?? false;
  bool hasClicked() => _clicked != null;

  // "clickedTime" field.
  DateTime? _clickedTime;
  DateTime? get clickedTime => _clickedTime;
  bool hasClickedTime() => _clickedTime != null;

  // "userid" field.
  DocumentReference? _userid;
  DocumentReference? get userid => _userid;
  bool hasUserid() => _userid != null;

  // "clicked2" field.
  List<DocumentReference>? _clicked2;
  List<DocumentReference> get clicked2 => _clicked2 ?? const [];
  bool hasClicked2() => _clicked2 != null;

  DocumentReference get parentReference => reference.parent.parent!;

  void _initializeFields() {
    _username = snapshotData['username'] as String?;
    _createdTime = snapshotData['createdTime'] as DateTime?;
    _storyImage = snapshotData['storyImage'] as String?;
    _storyid = snapshotData['storyid'] as DocumentReference?;
    _clicked = snapshotData['clicked'] as bool?;
    _clickedTime = snapshotData['clickedTime'] as DateTime?;
    _userid = snapshotData['userid'] as DocumentReference?;
    _clicked2 = getDataList(snapshotData['clicked2']);
  }

  static Query<Map<String, dynamic>> collection([DocumentReference? parent]) =>
      parent != null
          ? parent.collection('story')
          : FirebaseFirestore.instance.collectionGroup('story');

  static DocumentReference createDoc(DocumentReference parent, {String? id}) =>
      parent.collection('story').doc(id);

  static Stream<StoryRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => StoryRecord.fromSnapshot(s));

  static Future<StoryRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => StoryRecord.fromSnapshot(s));

  static StoryRecord fromSnapshot(DocumentSnapshot snapshot) => StoryRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static StoryRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      StoryRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'StoryRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is StoryRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createStoryRecordData({
  String? username,
  DateTime? createdTime,
  String? storyImage,
  DocumentReference? storyid,
  bool? clicked,
  DateTime? clickedTime,
  DocumentReference? userid,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'username': username,
      'createdTime': createdTime,
      'storyImage': storyImage,
      'storyid': storyid,
      'clicked': clicked,
      'clickedTime': clickedTime,
      'userid': userid,
    }.withoutNulls,
  );

  return firestoreData;
}

class StoryRecordDocumentEquality implements Equality<StoryRecord> {
  const StoryRecordDocumentEquality();

  @override
  bool equals(StoryRecord? e1, StoryRecord? e2) {
    const listEquality = ListEquality();
    return e1?.username == e2?.username &&
        e1?.createdTime == e2?.createdTime &&
        e1?.storyImage == e2?.storyImage &&
        e1?.storyid == e2?.storyid &&
        e1?.clicked == e2?.clicked &&
        e1?.clickedTime == e2?.clickedTime &&
        e1?.userid == e2?.userid &&
        listEquality.equals(e1?.clicked2, e2?.clicked2);
  }

  @override
  int hash(StoryRecord? e) => const ListEquality().hash([
        e?.username,
        e?.createdTime,
        e?.storyImage,
        e?.storyid,
        e?.clicked,
        e?.clickedTime,
        e?.userid,
        e?.clicked2
      ]);

  @override
  bool isValidKey(Object? o) => o is StoryRecord;
}
