import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class PagesRecord extends FirestoreRecord {
  PagesRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "title" field.
  String? _title;
  String get title => _title ?? '';
  bool hasTitle() => _title != null;

  // "description" field.
  String? _description;
  String get description => _description ?? '';
  bool hasDescription() => _description != null;

  // "category" field.
  String? _category;
  String get category => _category ?? '';
  bool hasCategory() => _category != null;

  // "website" field.
  String? _website;
  String get website => _website ?? '';
  bool hasWebsite() => _website != null;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  bool hasImage() => _image != null;

  // "likes" field.
  int? _likes;
  int get likes => _likes ?? 0;
  bool hasLikes() => _likes != null;

  // "admin" field.
  List<DocumentReference>? _admin;
  List<DocumentReference> get admin => _admin ?? const [];
  bool hasAdmin() => _admin != null;

  // "hastags" field.
  List<String>? _hastags;
  List<String> get hastags => _hastags ?? const [];
  bool hasHastags() => _hastags != null;

  // "followers" field.
  List<DocumentReference>? _followers;
  List<DocumentReference> get followers => _followers ?? const [];
  bool hasFollowers() => _followers != null;

  // "likesUsers" field.
  List<DocumentReference>? _likesUsers;
  List<DocumentReference> get likesUsers => _likesUsers ?? const [];
  bool hasLikesUsers() => _likesUsers != null;

  // "likesBool" field.
  bool? _likesBool;
  bool get likesBool => _likesBool ?? false;
  bool hasLikesBool() => _likesBool != null;

  void _initializeFields() {
    _title = snapshotData['title'] as String?;
    _description = snapshotData['description'] as String?;
    _category = snapshotData['category'] as String?;
    _website = snapshotData['website'] as String?;
    _image = snapshotData['image'] as String?;
    _likes = castToType<int>(snapshotData['likes']);
    _admin = getDataList(snapshotData['admin']);
    _hastags = getDataList(snapshotData['hastags']);
    _followers = getDataList(snapshotData['followers']);
    _likesUsers = getDataList(snapshotData['likesUsers']);
    _likesBool = snapshotData['likesBool'] as bool?;
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('pages');

  static Stream<PagesRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => PagesRecord.fromSnapshot(s));

  static Future<PagesRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => PagesRecord.fromSnapshot(s));

  static PagesRecord fromSnapshot(DocumentSnapshot snapshot) => PagesRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static PagesRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      PagesRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'PagesRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is PagesRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createPagesRecordData({
  String? title,
  String? description,
  String? category,
  String? website,
  String? image,
  int? likes,
  bool? likesBool,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'title': title,
      'description': description,
      'category': category,
      'website': website,
      'image': image,
      'likes': likes,
      'likesBool': likesBool,
    }.withoutNulls,
  );

  return firestoreData;
}

class PagesRecordDocumentEquality implements Equality<PagesRecord> {
  const PagesRecordDocumentEquality();

  @override
  bool equals(PagesRecord? e1, PagesRecord? e2) {
    const listEquality = ListEquality();
    return e1?.title == e2?.title &&
        e1?.description == e2?.description &&
        e1?.category == e2?.category &&
        e1?.website == e2?.website &&
        e1?.image == e2?.image &&
        e1?.likes == e2?.likes &&
        listEquality.equals(e1?.admin, e2?.admin) &&
        listEquality.equals(e1?.hastags, e2?.hastags) &&
        listEquality.equals(e1?.followers, e2?.followers) &&
        listEquality.equals(e1?.likesUsers, e2?.likesUsers) &&
        e1?.likesBool == e2?.likesBool;
  }

  @override
  int hash(PagesRecord? e) => const ListEquality().hash([
        e?.title,
        e?.description,
        e?.category,
        e?.website,
        e?.image,
        e?.likes,
        e?.admin,
        e?.hastags,
        e?.followers,
        e?.likesUsers,
        e?.likesBool
      ]);

  @override
  bool isValidKey(Object? o) => o is PagesRecord;
}
