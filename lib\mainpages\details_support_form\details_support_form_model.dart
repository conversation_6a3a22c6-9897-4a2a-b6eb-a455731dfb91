import '/flutter_flow/flutter_flow_util.dart';
import 'details_support_form_widget.dart' show DetailsSupportFormWidget;
import 'package:flutter/material.dart';

class DetailsSupportFormModel
    extends FlutterFlowModel<DetailsSupportFormWidget> {
  ///  State fields for stateful widgets in this page.

  // State field(s) for subject widget.
  FocusNode? subjectFocusNode;
  TextEditingController? subjectTextController;
  String? Function(BuildContext, String?)? subjectTextControllerValidator;
  // State field(s) for complaint widget.
  FocusNode? complaintFocusNode;
  TextEditingController? complaintTextController;
  String? Function(BuildContext, String?)? complaintTextControllerValidator;
  bool isDataUploading_uploadDataKi4 = false;
  FFUploadedFile uploadedLocalFile_uploadDataKi4 =
      FFUploadedFile(bytes: Uint8List.fromList([]));
  String uploadedFileUrl_uploadDataKi4 = '';

  bool isDataUploading_uploadDataD1l = false;
  FFUploadedFile uploadedLocalFile_uploadDataD1l =
      FFUploadedFile(bytes: Uint8List.fromList([]));

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    subjectFocusNode?.dispose();
    subjectTextController?.dispose();

    complaintFocusNode?.dispose();
    complaintTextController?.dispose();
  }
}
