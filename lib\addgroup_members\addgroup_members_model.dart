import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'addgroup_members_widget.dart' show AddgroupMembersWidget;
import 'package:flutter/material.dart';

class AddgroupMembersModel extends FlutterFlowModel<AddgroupMembersWidget> {
  ///  Local state fields for this page.

  List<String> hastagsList = [];
  void addToHastagsList(String item) => hastagsList.add(item);
  void removeFromHastagsList(String item) => hastagsList.remove(item);
  void removeAtIndexFromHastagsList(int index) => hastagsList.removeAt(index);
  void insertAtIndexInHastagsList(int index, String item) =>
      hastagsList.insert(index, item);
  void updateHastagsListAtIndex(int index, Function(String) updateFn) =>
      hastagsList[index] = updateFn(hastagsList[index]);

  ///  State fields for stateful widgets in this page.

  final formKey = GlobalKey<FormState>();
  // Stores action output result for [Backend Call - Create Document] action in invite widget.
  ActivityRecord? inviteUsers;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
