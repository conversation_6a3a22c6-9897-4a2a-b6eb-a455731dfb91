import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/push_notifications/push_notifications_util.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:google_fonts/google_fonts.dart';
import 'admin_page_model.dart';
export 'admin_page_model.dart';

class AdminPageWidget extends StatefulWidget {
  const AdminPageWidget({
    super.key,
    required this.groupRef,
    this.userRef,
  });

  final DocumentReference? groupRef;
  final DocumentReference? userRef;

  static String routeName = 'AdminPage';
  static String routePath = '/adminPage';

  @override
  State<AdminPageWidget> createState() => _AdminPageWidgetState();
}

class _AdminPageWidgetState extends State<AdminPageWidget> {
  late AdminPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => AdminPageModel());

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<GroupsRecord>(
      stream: GroupsRecord.getDocument(widget.groupRef!),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: SpinKitSquareCircle(
                  color: FlutterFlowTheme.of(context).secondary,
                  size: 50.0,
                ),
              ),
            ),
          );
        }

        final adminPageGroupsRecord = snapshot.data!;

        return GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
            appBar: AppBar(
              backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
              automaticallyImplyLeading: false,
              leading: Visibility(
                visible: responsiveVisibility(
                  context: context,
                  phone: false,
                  tablet: false,
                ),
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
                  child: InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                      scaffoldKey.currentState!.openDrawer();
                    },
                    child: Icon(
                      Icons.menu,
                      color: FlutterFlowTheme.of(context).secondaryText,
                      size: 29.0,
                    ),
                  ),
                ),
              ),
              title: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(
                        adminPageGroupsRecord.groupName,
                        style: FlutterFlowTheme.of(context)
                            .headlineMedium
                            .override(
                              font: GoogleFonts.outfit(
                                fontWeight: FlutterFlowTheme.of(context)
                                    .headlineMedium
                                    .fontWeight,
                                fontStyle: FlutterFlowTheme.of(context)
                                    .headlineMedium
                                    .fontStyle,
                              ),
                              letterSpacing: 0.0,
                              fontWeight: FlutterFlowTheme.of(context)
                                  .headlineMedium
                                  .fontWeight,
                              fontStyle: FlutterFlowTheme.of(context)
                                  .headlineMedium
                                  .fontStyle,
                            ),
                      ),
                    ],
                  ),
                ].divide(SizedBox(height: 4.0)),
              ),
              actions: [
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 12.0, 8.0),
                  child: FlutterFlowIconButton(
                    borderColor: FlutterFlowTheme.of(context).alternate,
                    borderRadius: 12.0,
                    borderWidth: 1.0,
                    buttonSize: 40.0,
                    fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                    icon: Icon(
                      Icons.close_rounded,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 24.0,
                    ),
                    onPressed: () async {
                      context.safePop();
                    },
                  ),
                ),
              ],
              centerTitle: false,
              elevation: 0.0,
            ),
            body: SafeArea(
              top: true,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  if (responsiveVisibility(
                    context: context,
                    tabletLandscape: false,
                    desktop: false,
                  ))
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Align(
                          alignment: AlignmentDirectional(-1.0, -1.0),
                          child: Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                17.0, 0.0, 0.0, 0.0),
                            child: StreamBuilder<UsersRecord>(
                              stream: UsersRecord.getDocument(widget.userRef!),
                              builder: (context, snapshot) {
                                // Customize what your widget looks like when it's loading.
                                if (!snapshot.hasData) {
                                  return Center(
                                    child: SizedBox(
                                      width: 50.0,
                                      height: 50.0,
                                      child: SpinKitSquareCircle(
                                        color: FlutterFlowTheme.of(context)
                                            .secondary,
                                        size: 50.0,
                                      ),
                                    ),
                                  );
                                }

                                final textUsersRecord = snapshot.data!;

                                return Text(
                                  '${textUsersRecord.displayName}from ${adminPageGroupsRecord.groupName}',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        font: GoogleFonts.readexPro(
                                          fontWeight:
                                              FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .fontWeight,
                                          fontStyle:
                                              FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .fontStyle,
                                        ),
                                        letterSpacing: 0.0,
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontStyle,
                                      ),
                                );
                              },
                            ),
                          ),
                        ),
                        if (adminPageGroupsRecord.nameBool ||
                            adminPageGroupsRecord.phoneBool ||
                            adminPageGroupsRecord.emailBool)
                          Align(
                            alignment: AlignmentDirectional(-1.0, -1.0),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  17.0, 29.0, 0.0, 0.0),
                              child: Text(
                                FFLocalizations.of(context).getText(
                                  '2ff936g1' /* Details you have asked */,
                                ),
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontStyle,
                                      ),
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontStyle,
                                    ),
                              ),
                            ),
                          ),
                        if (adminPageGroupsRecord.nameBool)
                          Align(
                            alignment: AlignmentDirectional(-1.0, -1.0),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  17.0, 20.0, 0.0, 0.0),
                              child: Text(
                                'Name: ${adminPageGroupsRecord.nameParticipant.where((e) => adminPageGroupsRecord.groupMembers.contains(widget.userRef)).toList().firstOrNull}',
                                maxLines: 5,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontStyle,
                                      ),
                                      fontSize: 19.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontStyle,
                                    ),
                              ),
                            ),
                          ),
                        if (adminPageGroupsRecord.phoneBool)
                          Align(
                            alignment: AlignmentDirectional(-1.0, -1.0),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  17.0, 20.0, 0.0, 0.0),
                              child: Text(
                                'Number:  ${adminPageGroupsRecord.numberParticipant.where((e) => adminPageGroupsRecord.groupMembers.contains(widget.userRef)).toList().firstOrNull}',
                                maxLines: 5,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontStyle,
                                      ),
                                      fontSize: 19.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontStyle,
                                    ),
                              ),
                            ),
                          ),
                        if (adminPageGroupsRecord.emailBool)
                          Align(
                            alignment: AlignmentDirectional(-1.0, -1.0),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  17.0, 20.0, 0.0, 0.0),
                              child: Text(
                                'Email:  ${adminPageGroupsRecord.emailParticipant.where((e) => adminPageGroupsRecord.groupMembers.contains(widget.userRef)).toList().firstOrNull}',
                                maxLines: 5,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .fontStyle,
                                      ),
                                      fontSize: 19.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .fontStyle,
                                    ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  if (responsiveVisibility(
                    context: context,
                    tabletLandscape: false,
                    desktop: false,
                  ))
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        if (adminPageGroupsRecord.groupPendingMember
                            .contains(widget.userRef))
                          Padding(
                            padding: EdgeInsets.all(16.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                await widget.groupRef!.update({
                                  ...mapToFirestore(
                                    {
                                      'group_pending_member':
                                          FieldValue.arrayRemove(
                                              [widget.userRef]),
                                      'group_members': FieldValue.arrayUnion(
                                          [widget.userRef]),
                                    },
                                  ),
                                });

                                var activityRecordReference =
                                    ActivityRecord.collection.doc();
                                await activityRecordReference.set({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Request Accepted',
                                    description:
                                        'Your Request for ${adminPageGroupsRecord.groupName} is accepted',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                });
                                _model.inviteUsers9 =
                                    ActivityRecord.getDocumentFromData({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Request Accepted',
                                    description:
                                        'Your Request for ${adminPageGroupsRecord.groupName} is accepted',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                }, activityRecordReference);
                                triggerPushNotification(
                                  notificationTitle: 'Request Accepted',
                                  notificationText:
                                      'Your Request for ${adminPageGroupsRecord.groupName} is accepted',
                                  userRefs: [widget.userRef!],
                                  initialPageName: 'Groupview',
                                  parameterData: {
                                    'groupRef': widget.groupRef,
                                  },
                                );

                                safeSetState(() {});
                              },
                              text: FFLocalizations.of(context).getText(
                                '6q516cz1' /* Accept Request */,
                              ),
                              options: FFButtonOptions(
                                width: () {
                                  if (MediaQuery.sizeOf(context).width <
                                      kBreakpointMedium) {
                                    return 50.0;
                                  } else if (MediaQuery.sizeOf(context).width <
                                      kBreakpointLarge) {
                                    return 50.0;
                                  } else if (MediaQuery.sizeOf(context).width <
                                      kBreakpointSmall) {
                                    return 90.0;
                                  } else {
                                    return 90.0;
                                  }
                                }(),
                                height: 40.0,
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: Color(0xFFF8C12B),
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontStyle,
                                      ),
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontStyle,
                                    ),
                                elevation: 3.0,
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        if (!adminPageGroupsRecord.groupModerator
                                .contains(widget.userRef) &&
                            adminPageGroupsRecord.groupMembers
                                .contains(widget.userRef) &&
                            adminPageGroupsRecord.groupModerator
                                .contains(currentUserReference))
                          Padding(
                            padding: EdgeInsets.all(16.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                await widget.groupRef!.update({
                                  ...mapToFirestore(
                                    {
                                      'group_moderator': FieldValue.arrayUnion(
                                          [widget.userRef]),
                                    },
                                  ),
                                });
                                triggerPushNotification(
                                  notificationTitle: 'Moderator Role given',
                                  notificationText:
                                      'You have given moderator role on group ${adminPageGroupsRecord.groupName}',
                                  userRefs: [widget.userRef!],
                                  initialPageName: 'Groupview',
                                  parameterData: {
                                    'groupRef': widget.groupRef,
                                  },
                                );

                                var activityRecordReference =
                                    ActivityRecord.collection.doc();
                                await activityRecordReference.set({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Moderator Role given',
                                    description:
                                        'You have given moderator role on group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                });
                                _model.inviteUsers =
                                    ActivityRecord.getDocumentFromData({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Moderator Role given',
                                    description:
                                        'You have given moderator role on group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                }, activityRecordReference);

                                safeSetState(() {});
                              },
                              text: FFLocalizations.of(context).getText(
                                'r2mc7ayh' /* Make Moderator */,
                              ),
                              options: FFButtonOptions(
                                width: double.infinity,
                                height: 40.0,
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: Color(0xFFF8C12B),
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontStyle,
                                      ),
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontStyle,
                                    ),
                                elevation: 3.0,
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        if (adminPageGroupsRecord.groupModerator
                                .contains(widget.userRef) &&
                            adminPageGroupsRecord.groupMembers
                                .contains(widget.userRef) &&
                            adminPageGroupsRecord.groupModerator
                                .contains(currentUserReference))
                          Padding(
                            padding: EdgeInsets.all(16.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                await widget.groupRef!.update({
                                  ...mapToFirestore(
                                    {
                                      'group_moderator': FieldValue.arrayRemove(
                                          [widget.userRef]),
                                    },
                                  ),
                                });
                                triggerPushNotification(
                                  notificationTitle:
                                      'Now, You are not a moderator',
                                  notificationText:
                                      'Now, You are not a moderator of group ${adminPageGroupsRecord.groupName}',
                                  userRefs: [widget.userRef!],
                                  initialPageName: 'Groupview',
                                  parameterData: {
                                    'groupRef': widget.groupRef,
                                  },
                                );

                                var activityRecordReference =
                                    ActivityRecord.collection.doc();
                                await activityRecordReference.set({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Now, You are not a moderator',
                                    description:
                                        'Now, You are not a moderator of group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                });
                                _model.inviteUsers7 =
                                    ActivityRecord.getDocumentFromData({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Now, You are not a moderator',
                                    description:
                                        'Now, You are not a moderator of group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                }, activityRecordReference);

                                safeSetState(() {});
                              },
                              text: FFLocalizations.of(context).getText(
                                'upko5o96' /* Remove from Role */,
                              ),
                              options: FFButtonOptions(
                                width: double.infinity,
                                height: 40.0,
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: FlutterFlowTheme.of(context).error,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontStyle,
                                      ),
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontStyle,
                                    ),
                                elevation: 3.0,
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        if (!adminPageGroupsRecord.groupPendingMember
                                .contains(widget.userRef) &&
                            !adminPageGroupsRecord.groupAdmins
                                .contains(widget.userRef))
                          Padding(
                            padding: EdgeInsets.all(16.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                await widget.groupRef!.update({
                                  ...mapToFirestore(
                                    {
                                      'group_members': FieldValue.arrayRemove(
                                          [widget.userRef]),
                                    },
                                  ),
                                });
                                triggerPushNotification(
                                  notificationTitle: 'Removed from Group',
                                  notificationText:
                                      'You are removed from group ${adminPageGroupsRecord.groupName}',
                                  userRefs: [widget.userRef!],
                                  initialPageName: 'notifications_List',
                                  parameterData: {},
                                );

                                var activityRecordReference =
                                    ActivityRecord.collection.doc();
                                await activityRecordReference.set({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Removed from Group',
                                    description:
                                        'You are removed from group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                });
                                _model.inviteUsers3 =
                                    ActivityRecord.getDocumentFromData({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Removed from Group',
                                    description:
                                        'You are removed from group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                }, activityRecordReference);

                                safeSetState(() {});
                              },
                              text: FFLocalizations.of(context).getText(
                                'q0ynu6sc' /* Remove from Group */,
                              ),
                              options: FFButtonOptions(
                                width: double.infinity,
                                height: 40.0,
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: FlutterFlowTheme.of(context).error,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontStyle,
                                      ),
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontStyle,
                                    ),
                                elevation: 3.0,
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        if (adminPageGroupsRecord.groupPendingMember
                            .contains(widget.userRef))
                          Padding(
                            padding: EdgeInsets.all(16.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                await widget.groupRef!.update({
                                  ...mapToFirestore(
                                    {
                                      'group_pending_member':
                                          FieldValue.arrayRemove(
                                              [widget.userRef]),
                                    },
                                  ),
                                });
                                triggerPushNotification(
                                  notificationTitle: 'Request Denied',
                                  notificationText:
                                      'Your Request Denied for joining group ${adminPageGroupsRecord.groupName}',
                                  userRefs: [widget.userRef!],
                                  initialPageName: 'notifications_List',
                                  parameterData: {},
                                );

                                var activityRecordReference =
                                    ActivityRecord.collection.doc();
                                await activityRecordReference.set({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Request Denied',
                                    description:
                                        'Your Request Denied for joining group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                });
                                _model.inviteUsers1 =
                                    ActivityRecord.getDocumentFromData({
                                  ...createActivityRecordData(
                                    timePosted: getCurrentTimestamp,
                                    owner: currentUserReference,
                                    type: 'Request Denied',
                                    description:
                                        'Your Request Denied for joining group ${adminPageGroupsRecord.groupName}',
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'notifyUsers': [widget.userRef],
                                    },
                                  ),
                                }, activityRecordReference);

                                safeSetState(() {});
                              },
                              text: FFLocalizations.of(context).getText(
                                'o1bzo4b5' /* Deny Request */,
                              ),
                              options: FFButtonOptions(
                                width: double.infinity,
                                height: 40.0,
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: FlutterFlowTheme.of(context).error,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      font: GoogleFonts.readexPro(
                                        fontWeight: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontWeight,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontStyle,
                                      ),
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontStyle,
                                    ),
                                elevation: 3.0,
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        Padding(
                          padding: EdgeInsets.all(16.0),
                          child: FFButtonWidget(
                            onPressed: () async {
                              context.pushNamed(HomePageWidget.routeName);
                            },
                            text: FFLocalizations.of(context).getText(
                              'qfh8fwgk' /* Delete Content Or Remove Post */,
                            ),
                            options: FFButtonOptions(
                              width: double.infinity,
                              height: 40.0,
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  24.0, 0.0, 24.0, 0.0),
                              iconPadding: EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).error,
                              textStyle: FlutterFlowTheme.of(context)
                                  .titleSmall
                                  .override(
                                    font: GoogleFonts.readexPro(
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .fontStyle,
                                    ),
                                    color: Colors.white,
                                    letterSpacing: 0.0,
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontStyle,
                                  ),
                              elevation: 3.0,
                              borderSide: BorderSide(
                                color: Colors.transparent,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ],
                    ),
                  if (responsiveVisibility(
                    context: context,
                    phone: false,
                    tablet: false,
                  ))
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          width: MediaQuery.sizeOf(context).width * 0.4,
                          decoration: BoxDecoration(),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Align(
                                alignment: AlignmentDirectional(-1.0, -1.0),
                                child: Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      17.0, 0.0, 0.0, 0.0),
                                  child: StreamBuilder<UsersRecord>(
                                    stream: UsersRecord.getDocument(
                                        widget.userRef!),
                                    builder: (context, snapshot) {
                                      // Customize what your widget looks like when it's loading.
                                      if (!snapshot.hasData) {
                                        return Center(
                                          child: SizedBox(
                                            width: 50.0,
                                            height: 50.0,
                                            child: SpinKitSquareCircle(
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondary,
                                              size: 50.0,
                                            ),
                                          ),
                                        );
                                      }

                                      final textUsersRecord = snapshot.data!;

                                      return Text(
                                        '${textUsersRecord.displayName}from ${adminPageGroupsRecord.groupName}',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              font: GoogleFonts.readexPro(
                                                fontWeight:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .fontWeight,
                                                fontStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .fontStyle,
                                              ),
                                              letterSpacing: 0.0,
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontStyle,
                                            ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              if (adminPageGroupsRecord.nameBool ||
                                  adminPageGroupsRecord.phoneBool ||
                                  adminPageGroupsRecord.emailBool)
                                Align(
                                  alignment: AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        17.0, 29.0, 0.0, 0.0),
                                    child: Text(
                                      FFLocalizations.of(context).getText(
                                        '1mg31u9w' /* Details you have asked */,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            font: GoogleFonts.readexPro(
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontStyle,
                                            ),
                                            letterSpacing: 0.0,
                                            fontWeight:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontWeight,
                                            fontStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontStyle,
                                          ),
                                    ),
                                  ),
                                ),
                              if (adminPageGroupsRecord.nameBool)
                                Align(
                                  alignment: AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        17.0, 20.0, 0.0, 0.0),
                                    child: Text(
                                      'Name: ${adminPageGroupsRecord.nameParticipant.where((e) => adminPageGroupsRecord.groupMembers.contains(widget.userRef)).toList().firstOrNull}',
                                      maxLines: 5,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            font: GoogleFonts.readexPro(
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontStyle,
                                            ),
                                            fontSize: 19.0,
                                            letterSpacing: 0.0,
                                            fontWeight:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontWeight,
                                            fontStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontStyle,
                                          ),
                                    ),
                                  ),
                                ),
                              if (adminPageGroupsRecord.phoneBool)
                                Align(
                                  alignment: AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        17.0, 20.0, 0.0, 0.0),
                                    child: Text(
                                      'Number:  ${adminPageGroupsRecord.numberParticipant.where((e) => adminPageGroupsRecord.groupMembers.contains(widget.userRef)).toList().firstOrNull}',
                                      maxLines: 5,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            font: GoogleFonts.readexPro(
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontStyle,
                                            ),
                                            fontSize: 19.0,
                                            letterSpacing: 0.0,
                                            fontWeight:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontWeight,
                                            fontStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontStyle,
                                          ),
                                    ),
                                  ),
                                ),
                              if (adminPageGroupsRecord.emailBool)
                                Align(
                                  alignment: AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        17.0, 20.0, 0.0, 0.0),
                                    child: Text(
                                      'Email:  ${adminPageGroupsRecord.emailParticipant.where((e) => adminPageGroupsRecord.groupMembers.contains(widget.userRef)).toList().firstOrNull}',
                                      maxLines: 5,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            font: GoogleFonts.readexPro(
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .fontStyle,
                                            ),
                                            fontSize: 19.0,
                                            letterSpacing: 0.0,
                                            fontWeight:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontWeight,
                                            fontStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyMedium
                                                    .fontStyle,
                                          ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Container(
                          width: MediaQuery.sizeOf(context).width * 0.55,
                          decoration: BoxDecoration(
                            color: FlutterFlowTheme.of(context)
                                .secondaryBackground,
                          ),
                          child: Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                2.0, 0.0, 0.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                if (adminPageGroupsRecord.groupPendingMember
                                    .contains(widget.userRef))
                                  Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: FFButtonWidget(
                                      onPressed: () async {
                                        await widget.groupRef!.update({
                                          ...mapToFirestore(
                                            {
                                              'group_pending_member':
                                                  FieldValue.arrayRemove(
                                                      [widget.userRef]),
                                              'group_members':
                                                  FieldValue.arrayUnion(
                                                      [widget.userRef]),
                                            },
                                          ),
                                        });

                                        await ActivityRecord.collection
                                            .doc()
                                            .set({
                                          ...createActivityRecordData(
                                            timePosted: getCurrentTimestamp,
                                            owner: currentUserReference,
                                            type: 'Request Accepted',
                                            description:
                                                'Your Request for ${adminPageGroupsRecord.groupName} is accepted',
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'notifyUsers': [widget.userRef],
                                            },
                                          ),
                                        });
                                        triggerPushNotification(
                                          notificationTitle: 'Request Accepted',
                                          notificationText:
                                              'Your Request for ${adminPageGroupsRecord.groupName} is accepted',
                                          userRefs: [widget.userRef!],
                                          initialPageName: 'Groupview',
                                          parameterData: {
                                            'groupRef': widget.groupRef,
                                          },
                                        );
                                      },
                                      text: FFLocalizations.of(context).getText(
                                        'jmw4jjb0' /* Accept Request */,
                                      ),
                                      options: FFButtonOptions(
                                        width: () {
                                          if (MediaQuery.sizeOf(context).width <
                                              kBreakpointMedium) {
                                            return 50.0;
                                          } else if (MediaQuery.sizeOf(context)
                                                  .width <
                                              kBreakpointLarge) {
                                            return 50.0;
                                          } else if (MediaQuery.sizeOf(context)
                                                  .width <
                                              kBreakpointSmall) {
                                            return 90.0;
                                          } else {
                                            return 90.0;
                                          }
                                        }(),
                                        height: 40.0,
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24.0, 0.0, 24.0, 0.0),
                                        iconPadding:
                                            EdgeInsetsDirectional.fromSTEB(
                                                0.0, 0.0, 0.0, 0.0),
                                        color: Color(0xFFF8C12B),
                                        textStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .override(
                                              font: GoogleFonts.readexPro(
                                                fontWeight:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontWeight,
                                                fontStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontStyle,
                                              ),
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontStyle,
                                            ),
                                        elevation: 3.0,
                                        borderSide: BorderSide(
                                          color: Colors.transparent,
                                          width: 1.0,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                    ),
                                  ),
                                if (!adminPageGroupsRecord.groupModerator
                                        .contains(widget.userRef) &&
                                    adminPageGroupsRecord.groupMembers
                                        .contains(widget.userRef) &&
                                    adminPageGroupsRecord.groupModerator
                                        .contains(currentUserReference))
                                  Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: FFButtonWidget(
                                      onPressed: () async {
                                        await widget.groupRef!.update({
                                          ...mapToFirestore(
                                            {
                                              'group_moderator':
                                                  FieldValue.arrayUnion(
                                                      [widget.userRef]),
                                            },
                                          ),
                                        });
                                        triggerPushNotification(
                                          notificationTitle:
                                              'Moderator Role given',
                                          notificationText:
                                              'You have given moderator role on group ${adminPageGroupsRecord.groupName}',
                                          userRefs: [widget.userRef!],
                                          initialPageName: 'Groupview',
                                          parameterData: {
                                            'groupRef': widget.groupRef,
                                          },
                                        );

                                        await ActivityRecord.collection
                                            .doc()
                                            .set({
                                          ...createActivityRecordData(
                                            timePosted: getCurrentTimestamp,
                                            owner: currentUserReference,
                                            type: 'Moderator Role given',
                                            description:
                                                'You have given moderator role on group ${adminPageGroupsRecord.groupName}',
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'notifyUsers': [widget.userRef],
                                            },
                                          ),
                                        });
                                      },
                                      text: FFLocalizations.of(context).getText(
                                        '3sxuysnj' /* Make Moderator */,
                                      ),
                                      options: FFButtonOptions(
                                        width: double.infinity,
                                        height: 40.0,
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24.0, 0.0, 24.0, 0.0),
                                        iconPadding:
                                            EdgeInsetsDirectional.fromSTEB(
                                                0.0, 0.0, 0.0, 0.0),
                                        color: Color(0xFFF8C12B),
                                        textStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .override(
                                              font: GoogleFonts.readexPro(
                                                fontWeight:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontWeight,
                                                fontStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontStyle,
                                              ),
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontStyle,
                                            ),
                                        elevation: 3.0,
                                        borderSide: BorderSide(
                                          color: Colors.transparent,
                                          width: 1.0,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                    ),
                                  ),
                                if (adminPageGroupsRecord.groupModerator
                                        .contains(widget.userRef) &&
                                    adminPageGroupsRecord.groupMembers
                                        .contains(widget.userRef) &&
                                    adminPageGroupsRecord.groupModerator
                                        .contains(currentUserReference))
                                  Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: FFButtonWidget(
                                      onPressed: () async {
                                        await widget.groupRef!.update({
                                          ...mapToFirestore(
                                            {
                                              'group_moderator':
                                                  FieldValue.arrayRemove(
                                                      [widget.userRef]),
                                            },
                                          ),
                                        });
                                        triggerPushNotification(
                                          notificationTitle:
                                              'Now, You are not a moderator',
                                          notificationText:
                                              'Now, You are not a moderator of group ${adminPageGroupsRecord.groupName}',
                                          userRefs: [widget.userRef!],
                                          initialPageName: 'Groupview',
                                          parameterData: {
                                            'groupRef': widget.groupRef,
                                          },
                                        );

                                        await ActivityRecord.collection
                                            .doc()
                                            .set({
                                          ...createActivityRecordData(
                                            timePosted: getCurrentTimestamp,
                                            owner: currentUserReference,
                                            type:
                                                'Now, You are not a moderator',
                                            description:
                                                'Now, You are not a moderator of group ${adminPageGroupsRecord.groupName}',
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'notifyUsers': [widget.userRef],
                                            },
                                          ),
                                        });
                                      },
                                      text: FFLocalizations.of(context).getText(
                                        'ye8ahpw7' /* Remove from Role */,
                                      ),
                                      options: FFButtonOptions(
                                        width: double.infinity,
                                        height: 40.0,
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24.0, 0.0, 24.0, 0.0),
                                        iconPadding:
                                            EdgeInsetsDirectional.fromSTEB(
                                                0.0, 0.0, 0.0, 0.0),
                                        color:
                                            FlutterFlowTheme.of(context).error,
                                        textStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .override(
                                              font: GoogleFonts.readexPro(
                                                fontWeight:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontWeight,
                                                fontStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontStyle,
                                              ),
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontStyle,
                                            ),
                                        elevation: 3.0,
                                        borderSide: BorderSide(
                                          color: Colors.transparent,
                                          width: 1.0,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                    ),
                                  ),
                                if (!adminPageGroupsRecord.groupPendingMember
                                        .contains(widget.userRef) &&
                                    !adminPageGroupsRecord.groupAdmins
                                        .contains(widget.userRef))
                                  Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: FFButtonWidget(
                                      onPressed: () async {
                                        await widget.groupRef!.update({
                                          ...mapToFirestore(
                                            {
                                              'group_members':
                                                  FieldValue.arrayRemove(
                                                      [widget.userRef]),
                                            },
                                          ),
                                        });
                                        triggerPushNotification(
                                          notificationTitle:
                                              'Removed from Group',
                                          notificationText:
                                              'You are removed from group ${adminPageGroupsRecord.groupName}',
                                          userRefs: [widget.userRef!],
                                          initialPageName: 'notifications_List',
                                          parameterData: {},
                                        );

                                        await ActivityRecord.collection
                                            .doc()
                                            .set({
                                          ...createActivityRecordData(
                                            timePosted: getCurrentTimestamp,
                                            owner: currentUserReference,
                                            type: 'Removed from Group',
                                            description:
                                                'You are removed from group ${adminPageGroupsRecord.groupName}',
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'notifyUsers': [widget.userRef],
                                            },
                                          ),
                                        });
                                      },
                                      text: FFLocalizations.of(context).getText(
                                        '4k501n2f' /* Remove from Group */,
                                      ),
                                      options: FFButtonOptions(
                                        width: double.infinity,
                                        height: 40.0,
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24.0, 0.0, 24.0, 0.0),
                                        iconPadding:
                                            EdgeInsetsDirectional.fromSTEB(
                                                0.0, 0.0, 0.0, 0.0),
                                        color:
                                            FlutterFlowTheme.of(context).error,
                                        textStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .override(
                                              font: GoogleFonts.readexPro(
                                                fontWeight:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontWeight,
                                                fontStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontStyle,
                                              ),
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontStyle,
                                            ),
                                        elevation: 3.0,
                                        borderSide: BorderSide(
                                          color: Colors.transparent,
                                          width: 1.0,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                    ),
                                  ),
                                if (adminPageGroupsRecord.groupPendingMember
                                    .contains(widget.userRef))
                                  Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: FFButtonWidget(
                                      onPressed: () async {
                                        await widget.groupRef!.update({
                                          ...mapToFirestore(
                                            {
                                              'group_pending_member':
                                                  FieldValue.arrayRemove(
                                                      [widget.userRef]),
                                            },
                                          ),
                                        });
                                        triggerPushNotification(
                                          notificationTitle: 'Request Denied',
                                          notificationText:
                                              'Your Request Denied for joining group ${adminPageGroupsRecord.groupName}',
                                          userRefs: [widget.userRef!],
                                          initialPageName: 'notifications_List',
                                          parameterData: {},
                                        );

                                        await ActivityRecord.collection
                                            .doc()
                                            .set({
                                          ...createActivityRecordData(
                                            timePosted: getCurrentTimestamp,
                                            owner: currentUserReference,
                                            type: 'Request Denied',
                                            description:
                                                'Your Request Denied for joining group ${adminPageGroupsRecord.groupName}',
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'notifyUsers': [widget.userRef],
                                            },
                                          ),
                                        });
                                      },
                                      text: FFLocalizations.of(context).getText(
                                        '6rv0ecs1' /* Deny Request */,
                                      ),
                                      options: FFButtonOptions(
                                        width: double.infinity,
                                        height: 40.0,
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24.0, 0.0, 24.0, 0.0),
                                        iconPadding:
                                            EdgeInsetsDirectional.fromSTEB(
                                                0.0, 0.0, 0.0, 0.0),
                                        color:
                                            FlutterFlowTheme.of(context).error,
                                        textStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .override(
                                              font: GoogleFonts.readexPro(
                                                fontWeight:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontWeight,
                                                fontStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .titleSmall
                                                        .fontStyle,
                                              ),
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              fontWeight:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontWeight,
                                              fontStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .titleSmall
                                                      .fontStyle,
                                            ),
                                        elevation: 3.0,
                                        borderSide: BorderSide(
                                          color: Colors.transparent,
                                          width: 1.0,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
