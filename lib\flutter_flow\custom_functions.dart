import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'lat_lng.dart';
import 'place.dart';
import 'uploaded_file.dart';
import '/backend/backend.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '/backend/schema/structs/index.dart';
import '/auth/firebase_auth/auth_util.dart';

String howLongAudioIs(String? aud) {
  // return how long the audio is in a minute format and if it is more than 60 mins then hour in string
  if (aud == null) {
    return '';
  }
  final int audioLength = int.tryParse(aud) ?? 0;
  final int minutes = audioLength ~/ 60;
  final int seconds = audioLength % 60;
  if (minutes < 60) {
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  } else {
    final int hours = minutes ~/ 60;
    final int remainingMinutes = minutes % 60;
    return '$hours:${remainingMinutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

String getPlaybackID(String url) {
  String str1 = url.replaceAll(".m3u8", "");
  return str1.split("/").last;
}

String? hastag(String? sqe) {
  // if sqe had # before the text then return it without #
  if (sqe == null) {
    return null;
  }
  if (sqe.startsWith('#')) {
    return sqe.substring(1);
  }
  return sqe;
}

List<String> shuffleAns(
  List<String> aer1,
  String aer2,
) {
  // make a new list and add aer2 in aer1 and shuffle their order using random
  aer1.add(aer2);
  aer1.shuffle(math.Random());
  return aer1;
}
