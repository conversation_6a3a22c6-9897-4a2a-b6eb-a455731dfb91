{"name": "cloud-functions", "description": "Firebase Cloud Functions", "dependencies": {"firebase-admin": "^11.11.0", "firebase-functions": "^4.4.1", "braintree": "^3.6.0", "@mux/mux-node": "^7.3.3", "stripe": "^8.0.1", "axios": "1.8.2", "razorpay": "^2.8.4", "qs": "^6.7.0", "@onesignal/node-onesignal": "^2.0.1-beta2", "@langchain/core": "^0.3.19", "@langchain/langgraph": "^0.2.23", "@langchain/openai": "^0.3.14", "@langchain/google-genai": "^0.0.8", "@langchain/anthropic": "^0.1.1"}, "devDependencies": {"eslint": "^6.8.0", "eslint-plugin-promise": "^4.2.1"}, "scripts": {"lint": "./node_modules/.bin/eslint --max-warnings=0 .", "serve": "firebase -P wisebook-2c9da emulators:start --only functions", "shell": "firebase -P wisebook-2c9da functions:shell", "start": "npm run shell", "logs": "firebase -P wisebook-2c9da functions:log", "compile": "cp ../../tsconfig.template.json ./tsconfig-compile.json && tsc --project tsconfig-compile.json"}, "engines": {"node": "20"}, "private": true}