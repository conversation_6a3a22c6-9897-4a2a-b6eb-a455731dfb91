// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class ScoringStruct extends FFFirebaseStruct {
  ScoringStruct({
    DocumentReference? users,
    int? score,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _users = users,
        _score = score,
        super(firestoreUtilData);

  // "users" field.
  DocumentReference? _users;
  DocumentReference? get users => _users;
  set users(DocumentReference? val) => _users = val;

  bool hasUsers() => _users != null;

  // "score" field.
  int? _score;
  int get score => _score ?? 0;
  set score(int? val) => _score = val;

  void incrementScore(int amount) => score = score + amount;

  bool hasScore() => _score != null;

  static ScoringStruct fromMap(Map<String, dynamic> data) => ScoringStruct(
        users: data['users'] as DocumentReference?,
        score: castToType<int>(data['score']),
      );

  static ScoringStruct? maybeFromMap(dynamic data) =>
      data is Map ? ScoringStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'users': _users,
        'score': _score,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'users': serializeParam(
          _users,
          ParamType.DocumentReference,
        ),
        'score': serializeParam(
          _score,
          ParamType.int,
        ),
      }.withoutNulls;

  static ScoringStruct fromSerializableMap(Map<String, dynamic> data) =>
      ScoringStruct(
        users: deserializeParam(
          data['users'],
          ParamType.DocumentReference,
          false,
          collectionNamePath: ['users'],
        ),
        score: deserializeParam(
          data['score'],
          ParamType.int,
          false,
        ),
      );

  @override
  String toString() => 'ScoringStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is ScoringStruct &&
        users == other.users &&
        score == other.score;
  }

  @override
  int get hashCode => const ListEquality().hash([users, score]);
}

ScoringStruct createScoringStruct({
  DocumentReference? users,
  int? score,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    ScoringStruct(
      users: users,
      score: score,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

ScoringStruct? updateScoringStruct(
  ScoringStruct? scoring, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    scoring
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addScoringStructData(
  Map<String, dynamic> firestoreData,
  ScoringStruct? scoring,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (scoring == null) {
    return;
  }
  if (scoring.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && scoring.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final scoringData = getScoringFirestoreData(scoring, forFieldValue);
  final nestedData = scoringData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = scoring.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getScoringFirestoreData(
  ScoringStruct? scoring, [
  bool forFieldValue = false,
]) {
  if (scoring == null) {
    return {};
  }
  final firestoreData = mapToFirestore(scoring.toMap());

  // Add any Firestore field values
  scoring.firestoreUtilData.fieldValues.forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getScoringListFirestoreData(
  List<ScoringStruct>? scorings,
) =>
    scorings?.map((e) => getScoringFirestoreData(e, true)).toList() ?? [];
