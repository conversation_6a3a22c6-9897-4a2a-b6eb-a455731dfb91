import '/flutter_flow/flutter_flow_util.dart';
import 'user_list_small_widget.dart' show UserListSmallWidget;
import 'package:flutter/material.dart';

class UserListSmallModel extends FlutterFlowModel<UserListSmallWidget> {
  ///  State fields for stateful widgets in this component.

  // State field(s) for iuser widget.
  bool iuserHovered = false;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
