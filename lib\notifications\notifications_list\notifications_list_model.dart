import '/flutter_flow/flutter_flow_util.dart';
import '/notifications/user_information/user_information_widget.dart';
import '/index.dart';
import 'notifications_list_widget.dart' show NotificationsListWidget;
import 'package:flutter/material.dart';

class NotificationsListModel extends FlutterFlowModel<NotificationsListWidget> {
  ///  State fields for stateful widgets in this page.

  // State field(s) for TabBar widget.
  TabController? tabBarController;
  int get tabBarCurrentIndex =>
      tabBarController != null ? tabBarController!.index : 0;
  int get tabBarPreviousIndex =>
      tabBarController != null ? tabBarController!.previousIndex : 0;

  // Models for userInformation dynamic component.
  late FlutterFlowDynamicModels<UserInformationModel> userInformationModels;

  @override
  void initState(BuildContext context) {
    userInformationModels =
        FlutterFlowDynamicModels(() => UserInformationModel());
  }

  @override
  void dispose() {
    tabBarController?.dispose();
    userInformationModels.dispose();
  }
}
