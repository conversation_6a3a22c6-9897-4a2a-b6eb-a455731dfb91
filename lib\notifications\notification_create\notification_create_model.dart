import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import '/notifications/user_list_small_1/user_list_small1_widget.dart';
import 'notification_create_widget.dart' show NotificationCreateWidget;
import 'package:flutter/material.dart';

class NotificationCreateModel
    extends FlutterFlowModel<NotificationCreateWidget> {
  ///  State fields for stateful widgets in this page.

  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode1;
  TextEditingController? textController1;
  String? Function(BuildContext, String?)? textController1Validator;
  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode2;
  TextEditingController? textController2;
  String? Function(BuildContext, String?)? textController2Validator;
  // State field(s) for ChoiceChips widget.
  FormFieldController<List<String>>? choiceChipsValueController;
  String? get choiceChipsValue =>
      choiceChipsValueController?.value?.firstOrNull;
  set choiceChipsValue(String? val) =>
      choiceChipsValueController?.value = val != null ? [val] : [];
  bool isDataUploading_uploadData3hnas = false;
  FFUploadedFile uploadedLocalFile_uploadData3hnas =
      FFUploadedFile(bytes: Uint8List.fromList([]));
  String uploadedFileUrl_uploadData3hnas = '';

  // Stores action output result for [Bottom Sheet - user_List] action in Container widget.
  UsersRecord? userRefSelected;
  // Model for user_ListSmall_1 component.
  late UserListSmall1Model userListSmall1Model;

  @override
  void initState(BuildContext context) {
    userListSmall1Model = createModel(context, () => UserListSmall1Model());
  }

  @override
  void dispose() {
    textFieldFocusNode1?.dispose();
    textController1?.dispose();

    textFieldFocusNode2?.dispose();
    textController2?.dispose();

    userListSmall1Model.dispose();
  }
}
