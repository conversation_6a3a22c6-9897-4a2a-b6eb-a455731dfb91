import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'create_comment_widget.dart' show CreateCommentWidget;
import 'package:flutter/material.dart';

class CreateCommentModel extends FlutterFlowModel<CreateCommentWidget> {
  ///  State fields for stateful widgets in this page.

  // State field(s) for CommentFiled widget.
  FocusNode? commentFiledFocusNode;
  TextEditingController? commentFiledTextController;
  String? Function(BuildContext, String?)? commentFiledTextControllerValidator;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    commentFiledFocusNode?.dispose();
    commentFiledTextController?.dispose();
  }
}
