import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/push_notifications/push_notifications_util.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_checkbox_group.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/form_field_controller.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'details_quiz_page_model.dart';
export 'details_quiz_page_model.dart';

class DetailsQuizPageWidget extends StatefulWidget {
  const DetailsQuizPageWidget({
    super.key,
    this.competitonRef,
  });

  final CompettionRecord? competitonRef;

  static String routeName = 'DetailsQuizPage';
  static String routePath = '/detailsQuizPage';

  @override
  State<DetailsQuizPageWidget> createState() => _DetailsQuizPageWidgetState();
}

class _DetailsQuizPageWidgetState extends State<DetailsQuizPageWidget>
    with TickerProviderStateMixin {
  late DetailsQuizPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = <String, AnimationInfo>{};

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DetailsQuizPageModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      _model.numberOfQuestions = widget.competitonRef?.quizQuestions.length;
      _model.correct = widget.competitonRef!.quizQuestions
          .elementAtOrNull(_model.int1)!
          .correctOne;
      safeSetState(() {});
    });

    animationsMap.addAll({
      'textOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          FadeEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
          MoveEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: Offset(0.0, 70.0),
            end: Offset(0.0, 0.0),
          ),
        ],
      ),
      'checkboxGroupOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          FadeEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
          MoveEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: Offset(0.0, 70.0),
            end: Offset(0.0, 0.0),
          ),
        ],
      ),
    });
    setupAnimations(
      animationsMap.values.where((anim) =>
          anim.trigger == AnimationTrigger.onActionTrigger ||
          !anim.applyInitialState),
      this,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<CompettionRecord>(
      stream: CompettionRecord.getDocument(widget.competitonRef!.reference),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: Color(0xFFF1F5F8),
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: SpinKitSquareCircle(
                  color: FlutterFlowTheme.of(context).secondary,
                  size: 50.0,
                ),
              ),
            ),
          );
        }

        final detailsQuizPageCompettionRecord = snapshot.data!;

        return GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: Color(0xFFF1F5F8),
            appBar: AppBar(
              backgroundColor: Color(0xFFF1F5F8),
              automaticallyImplyLeading: false,
              title: Text(
                detailsQuizPageCompettionRecord.competitonName,
                style: FlutterFlowTheme.of(context).displaySmall.override(
                      font: GoogleFonts.outfit(
                        fontWeight: FontWeight.w500,
                        fontStyle:
                            FlutterFlowTheme.of(context).displaySmall.fontStyle,
                      ),
                      color: Color(0xFF0F1113),
                      fontSize: 22.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w500,
                      fontStyle:
                          FlutterFlowTheme.of(context).displaySmall.fontStyle,
                    ),
              ),
              actions: [
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 16.0, 0.0),
                  child: FlutterFlowIconButton(
                    borderColor: Colors.transparent,
                    borderRadius: 30.0,
                    borderWidth: 1.0,
                    buttonSize: 50.0,
                    fillColor: Color(0xFFF1F5F8),
                    icon: Icon(
                      Icons.arrow_back,
                      color: Color(0xFF57636C),
                      size: 30.0,
                    ),
                    onPressed: () async {
                      context.safePop();
                    },
                  ),
                ),
              ],
              centerTitle: false,
              elevation: 0.0,
            ),
            body: SafeArea(
              top: true,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Align(
                            alignment: AlignmentDirectional(-1.0, 0.0),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  16.0, 12.0, 0.0, 0.0),
                              child: Text(
                                'Question ${(_model.int1 + 1).toString()}/${detailsQuizPageCompettionRecord.quizQuestions.length.toString()}',
                                style: FlutterFlowTheme.of(context)
                                    .labelMedium
                                    .override(
                                      font: GoogleFonts.plusJakartaSans(
                                        fontWeight: FontWeight.w500,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .labelMedium
                                            .fontStyle,
                                      ),
                                      color: Color(0xFF57636C),
                                      fontSize: 14.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w500,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .labelMedium
                                          .fontStyle,
                                    ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                8.0, 12.0, 8.0, 0.0),
                            child: LinearPercentIndicator(
                              percent: (_model.int1 + 1) /
                                  detailsQuizPageCompettionRecord
                                      .quizQuestions.length,
                              width: MediaQuery.sizeOf(context).width * 0.96,
                              lineHeight: 12.0,
                              animation: true,
                              animateFromLastPercent: true,
                              progressColor: Color(0xFF827AE1),
                              backgroundColor: Color(0xFFE0E3E7),
                              barRadius: Radius.circular(24.0),
                              padding: EdgeInsets.zero,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                16.0, 48.0, 0.0, 0.0),
                            child: Text(
                              detailsQuizPageCompettionRecord.quizQuestions
                                  .elementAtOrNull(_model.int1)!
                                  .question,
                              maxLines: 10,
                              style: FlutterFlowTheme.of(context)
                                  .labelLarge
                                  .override(
                                    font: GoogleFonts.plusJakartaSans(
                                      fontWeight: FontWeight.w600,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontStyle,
                                    ),
                                    color: Colors.black,
                                    fontSize: 16.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w600,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontStyle,
                                  ),
                            ).animateOnPageLoad(
                                animationsMap['textOnPageLoadAnimation']!),
                          ),
                          Wrap(
                            spacing: 0.0,
                            runSpacing: 0.0,
                            alignment: WrapAlignment.start,
                            crossAxisAlignment: WrapCrossAlignment.start,
                            direction: Axis.horizontal,
                            runAlignment: WrapAlignment.center,
                            verticalDirection: VerticalDirection.down,
                            clipBehavior: Clip.none,
                            children: [
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 12.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Expanded(
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            0.0, 12.0, 0.0, 12.0),
                                        child: FlutterFlowCheckboxGroup(
                                          options: functions
                                              .shuffleAns(
                                                  detailsQuizPageCompettionRecord
                                                      .quizQuestions
                                                      .map((e) => e.answers
                                                          .elementAtOrNull(
                                                              _model.int1))
                                                      .withoutNulls
                                                      .toList(),
                                                  detailsQuizPageCompettionRecord
                                                      .quizQuestions
                                                      .elementAtOrNull(
                                                          _model.int1)!
                                                      .correctOne)
                                              .toList(),
                                          onChanged: (val) async {
                                            safeSetState(() => _model
                                                .checkboxGroupValues = val);
                                            if (detailsQuizPageCompettionRecord
                                                    .quizQuestions
                                                    .elementAtOrNull(
                                                        _model.int1)
                                                    ?.correctOne ==
                                                _model.correct) {
                                              _model.numCorrectAns =
                                                  _model.numCorrectAns + 1;
                                              safeSetState(() {});
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    'Correct !!!',
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                  duration: Duration(
                                                      milliseconds: 4000),
                                                  backgroundColor:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .secondary,
                                                ),
                                              );
                                            } else {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    'Incorrect ',
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                  duration: Duration(
                                                      milliseconds: 4000),
                                                  backgroundColor:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .error,
                                                ),
                                              );
                                            }
                                          },
                                          controller: _model
                                                  .checkboxGroupValueController ??=
                                              FormFieldController<List<String>>(
                                            List.from([''] ?? []),
                                          ),
                                          activeColor: Color(0xFF827AE1),
                                          checkColor: Colors.white,
                                          checkboxBorderColor:
                                              Color(0xFF57636C),
                                          textStyle: FlutterFlowTheme.of(
                                                  context)
                                              .labelLarge
                                              .override(
                                                font:
                                                    GoogleFonts.plusJakartaSans(
                                                  fontWeight: FontWeight.w500,
                                                  fontStyle:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .labelLarge
                                                          .fontStyle,
                                                ),
                                                color: Color(0xFF57636C),
                                                fontSize: 16.0,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.w500,
                                                fontStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .labelLarge
                                                        .fontStyle,
                                              ),
                                          itemPadding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 12.0),
                                          checkboxBorderRadius:
                                              BorderRadius.circular(4.0),
                                          initialized:
                                              _model.checkboxGroupValues !=
                                                  null,
                                        ).animateOnPageLoad(animationsMap[
                                            'checkboxGroupOnPageLoadAnimation']!),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            0.0, 32.0, 0.0, 32.0),
                        child: FFButtonWidget(
                          onPressed: () async {
                            while (_model.numberOfQuestions! > _model.int1) {
                              _model.int1 = _model.int1 + 1;
                              safeSetState(() {});
                            }
                            if ((_model.numCorrectAns ==
                                    _model.numberOfQuestions) &&
                                !detailsQuizPageCompettionRecord.winnerBool) {
                              await widget.competitonRef!.reference
                                  .update(createCompettionRecordData(
                                participants: updateScoringStruct(
                                  ScoringStruct(
                                    users: currentUserReference,
                                    score: _model.numCorrectAns,
                                  ),
                                  clearUnsetFields: false,
                                ),
                                winner: currentUserReference,
                                winnerBool: true,
                              ));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'You win this competition !!!',
                                    style: TextStyle(
                                      color: Colors.white,
                                    ),
                                  ),
                                  duration: Duration(milliseconds: 4000),
                                  backgroundColor:
                                      FlutterFlowTheme.of(context).secondary,
                                ),
                              );
                              triggerPushNotification(
                                notificationTitle: 'Winner',
                                notificationText:
                                    '${detailsQuizPageCompettionRecord.competitonName}has been won',
                                userRefs: [
                                  detailsQuizPageCompettionRecord.host!
                                ],
                                initialPageName: 'notifications_List',
                                parameterData: {},
                              );

                              await ActivityRecord.collection.doc().set({
                                ...createActivityRecordData(
                                  description:
                                      '${detailsQuizPageCompettionRecord.competitonName}has been won',
                                  timePosted: getCurrentTimestamp,
                                  type: 'Winner',
                                ),
                                ...mapToFirestore(
                                  {
                                    'notifyUsers': [
                                      detailsQuizPageCompettionRecord.host
                                    ],
                                  },
                                ),
                              });
                            } else {
                              if (_model.numCorrectAns ==
                                  _model.numberOfQuestions) {
                                await widget.competitonRef!.reference.update({
                                  ...createCompettionRecordData(
                                    participants: updateScoringStruct(
                                      ScoringStruct(
                                        users: currentUserReference,
                                        score: _model.numCorrectAns,
                                      ),
                                      clearUnsetFields: false,
                                    ),
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'testers': FieldValue.arrayUnion(
                                          [currentUserReference]),
                                    },
                                  ),
                                });
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'All answers are correct !!!',
                                      style: TextStyle(
                                        color: Colors.white,
                                      ),
                                    ),
                                    duration: Duration(milliseconds: 4000),
                                    backgroundColor:
                                        FlutterFlowTheme.of(context).secondary,
                                  ),
                                );
                              } else {
                                await widget.competitonRef!.reference.update({
                                  ...createCompettionRecordData(
                                    participants: updateScoringStruct(
                                      ScoringStruct(
                                        users: currentUserReference,
                                        score: _model.numCorrectAns,
                                      ),
                                      clearUnsetFields: false,
                                    ),
                                  ),
                                  ...mapToFirestore(
                                    {
                                      'testers': FieldValue.arrayUnion(
                                          [currentUserReference]),
                                    },
                                  ),
                                });
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Your Score is ${_model.numCorrectAns.toString()}/${_model.numberOfQuestions?.toString()}',
                                      style: TextStyle(
                                        color: Colors.white,
                                      ),
                                    ),
                                    duration: Duration(milliseconds: 4000),
                                    backgroundColor:
                                        FlutterFlowTheme.of(context).secondary,
                                  ),
                                );
                              }
                            }
                          },
                          text: () {
                            if (_model.numberOfQuestions == (_model.int1 + 1)) {
                              return 'Submit';
                            } else if (detailsQuizPageCompettionRecord.testers
                                .contains(currentUserReference)) {
                              return 'You have already completed it';
                            } else if (detailsQuizPageCompettionRecord.winner ==
                                currentUserReference) {
                              return 'You have won ';
                            } else if (detailsQuizPageCompettionRecord.host ==
                                currentUserReference) {
                              return 'You created this competition';
                            } else {
                              return 'Next Question';
                            }
                          }(),
                          options: FFButtonOptions(
                            width: 300.0,
                            height: 50.0,
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            iconPadding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: Color(0xFF827AE1),
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  font: GoogleFonts.outfit(
                                    fontWeight: FontWeight.normal,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .fontStyle,
                                  ),
                                  color: Colors.white,
                                  fontSize: 16.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.normal,
                                  fontStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .fontStyle,
                                ),
                            elevation: 3.0,
                            borderSide: BorderSide(
                              color: Colors.transparent,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(40.0),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
