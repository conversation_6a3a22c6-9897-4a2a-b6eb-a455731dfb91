name: wise_book
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.1+13

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  apivideo_live_stream: 1.0.7
  assets_audio_player: 
    git:
      url: https://github.com/FlutterFlow/Flutter-AssetsAudioPlayer.git
      ref: 243f9533279b73fa9dae345cc96e5d7d84cac0ac
  assets_audio_player_web: 
    git:
      url: https://github.com/FlutterFlow/Flutter-AssetsAudioPlayerWeb.git
      ref: cae7ceb6af99f4da1ec27136d312d0ef045c83d9
  auto_size_text: 3.0.0
  cached_network_image: 3.4.1
  cached_network_image_platform_interface: 4.1.1
  cached_network_image_web: 1.3.1
  chewie: 1.11.3
  cloud_firestore: 5.6.9
  cloud_firestore_platform_interface: 6.6.9
  cloud_firestore_web: 4.4.9
  cloud_functions: 5.5.2
  cloud_functions_platform_interface: 5.7.2
  cloud_functions_web: 4.11.2
  collection: 1.19.1
  cross_file: 0.3.4+2
  csslib: 1.0.2
  device_info_plus: 11.5.0
  device_info_plus_platform_interface: 7.0.3
  dropdown_button2: 2.3.9
  easy_debounce: 2.0.1
  emoji_flag_converter: 1.1.0
  equatable: 2.0.7
  file_picker: 10.1.9
  firebase_auth: 5.6.0
  firebase_auth_platform_interface: 7.7.0
  firebase_auth_web: 5.15.0
  firebase_core: 3.14.0
  firebase_core_platform_interface: 5.4.0
  firebase_core_web: 2.23.0
  firebase_dynamic_links: 6.1.6
  firebase_dynamic_links_platform_interface: 0.2.7+7
  firebase_messaging: 15.2.7
  firebase_messaging_platform_interface: 4.6.7
  firebase_messaging_web: 3.10.7
  firebase_performance: 0.10.1+7
  firebase_performance_platform_interface: 0.1.5+7
  firebase_performance_web: 0.1.7+13
  firebase_storage: 12.4.7
  firebase_storage_platform_interface: 5.2.7
  firebase_storage_web: 3.10.14
  floating_bottom_navigation_bar: 1.5.2
  flutter_animate: 4.5.0
  flutter_cache_manager: 3.4.1
  flutter_plugin_android_lifecycle: 2.0.28
  flutter_rating_bar: 4.0.1
  flutter_spinkit: 5.2.0
  font_awesome_flutter: 10.7.0
  from_css_color: 2.0.0
  go_router: 12.1.3
  google_fonts: 6.1.0
  google_sign_in: 6.3.0
  google_sign_in_android: 6.2.1
  google_sign_in_ios: 5.9.0
  google_sign_in_platform_interface: 2.5.0
  google_sign_in_web: 0.12.4+4
  html: 0.15.6
  http: 1.4.0
  image_picker: 1.1.2
  image_picker_android: 0.8.12+23
  image_picker_for_web: 3.0.6
  image_picker_ios: 0.8.12+2
  image_picker_linux: 0.2.1+2
  image_picker_macos: 0.2.1+2
  image_picker_platform_interface: 2.10.1
  image_picker_windows: 0.2.1+1
  infinite_scroll_pagination: 4.0.0
  intl: 0.20.2
  json_path: 0.7.2
  mask_text_input_formatter: 2.9.0
  mime_type: 1.0.0
  native_device_orientation: 1.2.1
  page_transition: 2.1.0
  path_provider: 2.1.4
  path_provider_android: 2.2.10
  path_provider_foundation: 2.4.0
  path_provider_linux: 2.2.1
  path_provider_platform_interface: 2.1.2
  path_provider_windows: 2.3.0
  percent_indicator: 4.2.2
  permission_handler: 12.0.0+1
  permission_handler_android: 13.0.1
  permission_handler_apple: 9.4.7
  permission_handler_html: 0.1.3+5
  permission_handler_platform_interface: 4.3.0
  permission_handler_windows: 0.2.1
  pin_code_fields: 8.0.1
  platform_detect: 2.1.5
  plugin_platform_interface: 2.1.8
  pointer_interceptor: 0.10.1+2
  pointer_interceptor_ios: 0.10.1
  pointer_interceptor_platform_interface: 0.10.0+1
  pointer_interceptor_web: 0.10.2+1
  provider: 6.1.5
  record: 6.0.0
  record_android: 1.3.3
  record_ios: 1.0.0
  record_linux: 1.1.0
  record_macos: 1.0.0
  record_platform_interface: 1.2.0
  record_web: 1.1.8
  record_windows: 1.0.6
  rxdart: 0.27.7
  share_plus: 10.0.2
  share_plus_platform_interface: 5.0.0
  shared_preferences: 2.5.3
  shared_preferences_android: 2.4.10
  shared_preferences_foundation: 2.5.4
  shared_preferences_linux: 2.4.1
  shared_preferences_platform_interface: 2.4.1
  shared_preferences_web: 2.4.3
  shared_preferences_windows: 2.4.1
  sign_in_with_apple: 7.0.1
  sign_in_with_apple_platform_interface: 2.0.0
  sign_in_with_apple_web: 3.0.0
  smooth_page_indicator: 1.1.0
  sqflite: 2.3.3+1
  sqflite_common: 2.5.4+3
  stream_transform: 2.1.0
  text_search: 1.0.1
  timeago: 3.7.1
  url_launcher: 6.3.1
  url_launcher_android: 6.3.16
  url_launcher_ios: 6.3.3
  url_launcher_linux: 3.2.1
  url_launcher_macos: 3.2.2
  url_launcher_platform_interface: 2.3.2
  url_launcher_web: 2.4.1
  url_launcher_windows: 3.1.4
  uuid: ^4.0.0
  video_player: 2.10.0
  video_player_android: 2.8.7
  video_player_avfoundation: 2.7.1
  video_player_platform_interface: 6.3.0
  video_player_web: 2.3.5
  wakelock_plus: 1.3.2
  wakelock_plus_platform_interface: 1.2.3
  webview_flutter: 4.13.0
  webview_flutter_android: 4.7.0
  webview_flutter_platform_interface: 2.13.1
  webview_flutter_wkwebview: 3.22.0
  webviewx_plus: 
    git:
      url: https://github.com/FlutterFlow/webviewx_plus.git
      ref: 4f84ece5e06d52643c5d844f41880baf8440c584
  youtube_player_iframe: 5.2.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0

dependency_overrides:
  http: 1.4.0
  uuid: ^4.0.0

dev_dependencies:
  flutter_launcher_icons: 0.13.1
  flutter_lints: 4.0.0
  image: 4.2.0
  lints: 4.0.0
  flutter_test:
    sdk: flutter


flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  web:
    generate: true
  image_path: 'assets/images/app_launcher_icon.png'


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/jsons/



  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

