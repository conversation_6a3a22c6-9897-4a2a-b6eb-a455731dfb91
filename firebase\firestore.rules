rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /chat_messages/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /chats/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if /databases/$(database)/documents/users/$(request.auth.uid) in resource.data.users;
    }

    match /user/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if true;
      allow delete: if request.auth != null;
    }

    match /streams/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if request.auth.uid == resource.data.uid;
    }

    match /groups/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if false;
    }

    match /activity/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /users/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /users/{parent}/post/{document} {
      allow create: if request.auth != null;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if resource.data.post_user == /databases/$(database)/documents/users/$(request.auth.uid);
    }

    match /{path=**}/post/{document} {
      allow read: if true;
    }


    match /users/{parent}/story/{document} {
      allow create: if request.auth != null;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if resource.data.userid == /databases/$(database)/documents/users/$(request.auth.uid);
    }

    match /{path=**}/story/{document} {
      allow read: if true;
    }


    match /users/{parent}/savedPsts/{document} {
      allow create: if true;
      allow read: if request.auth != null;
      allow write: if resource.data.savedPostUser == /databases/$(database)/documents/users/$(request.auth.uid);
      allow delete: if resource.data.savedPostUser == /databases/$(database)/documents/users/$(request.auth.uid);
    }

    match /{path=**}/savedPsts/{document} {
      allow read: if request.auth != null;
    }


    match /users/{parent}/notification/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if true;
      allow delete: if true;
    }

    match /{path=**}/notification/{document} {
      allow read: if true;
    }


    match /users/{parent}/search/{document} {
      allow create: if request.auth != null;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /{path=**}/search/{document} {
      allow read: if true;
    }


    match /users/{parent}/comments/{document} {
      allow create: if request.auth != null;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if resource.data.commentUser == /databases/$(database)/documents/users/$(request.auth.uid);
    }

    match /{path=**}/comments/{document} {
      allow read: if true;
    }


    match /pages/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if /databases/$(database)/documents/users/$(request.auth.uid) in resource.data.admin;
    }

    match /groups/{parent}/GroupPost/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if false;
    }

    match /{path=**}/GroupPost/{document} {
      allow read: if true;
    }


    match /type/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if true;
      allow delete: if false;
    }

    match /compettion/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if resource.data.host == /databases/$(database)/documents/users/$(request.auth.uid);
    }

    match /ff_user_push_notifications/{id} {
      allow read, update, delete: if false;
      allow create: if /databases/$(database)/documents/users/$(request.auth.uid) == request.resource.data.sender;
    }
  }
}
